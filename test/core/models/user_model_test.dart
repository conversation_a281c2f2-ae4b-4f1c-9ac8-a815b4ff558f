import 'package:flutter_test/flutter_test.dart';
import 'package:campus_find/core/models/user_model.dart';
import 'package:campus_find/core/models/accommodation_model.dart';

void main() {
  group('UserModel', () {
    late UserModel validUser;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 1);
      validUser = UserModel(
        id: 'test-user-id',
        email: '<EMAIL>',
        fullName: 'Test User',
        phoneNumber: '+2348012345678',
        campus: 'University of Lagos',
        profileImageUrl: 'https://example.com/profile.jpg',
        createdAt: testDate,
        lastLoginAt: testDate,
        isVerified: true,
        role: UserRole.student,
      );
    });

    group('validation', () {
      test('should return empty list for valid user', () {
        final errors = validUser.validate();
        expect(errors, isEmpty);
      });

      test('should return error for empty id', () {
        final user = validUser.copyWith(id: '');
        final errors = user.validate();
        expect(errors, contains('User ID cannot be empty'));
      });

      test('should return error for empty email', () {
        final user = validUser.copyWith(email: '');
        final errors = user.validate();
        expect(errors, contains('Email cannot be empty'));
      });

      test('should return error for invalid email format', () {
        final user = validUser.copyWith(email: 'invalid-email');
        final errors = user.validate();
        expect(errors, contains('Invalid email format'));
      });

      test('should return error for empty full name', () {
        final user = validUser.copyWith(fullName: '');
        final errors = user.validate();
        expect(errors, contains('Full name cannot be empty'));
      });

      test('should return error for short full name', () {
        final user = validUser.copyWith(fullName: 'A');
        final errors = user.validate();
        expect(errors, contains('Full name must be at least 2 characters long'));
      });

      test('should return error for invalid phone number', () {
        final user = validUser.copyWith(phoneNumber: '123');
        final errors = user.validate();
        expect(errors, contains('Invalid phone number format'));
      });

      test('should accept null phone number', () {
        final user = validUser.copyWith(phoneNumber: null);
        final errors = user.validate();
        expect(errors, isEmpty);
      });

      test('should accept empty phone number', () {
        final user = validUser.copyWith(phoneNumber: '');
        final errors = user.validate();
        expect(errors, isEmpty);
      });
    });

    group('isValid getter', () {
      test('should return true for valid user', () {
        expect(validUser.isValid, isTrue);
      });

      test('should return false for invalid user', () {
        final user = validUser.copyWith(email: 'invalid-email');
        expect(user.isValid, isFalse);
      });
    });

    group('displayName getter', () {
      test('should return full name when available', () {
        expect(validUser.displayName, equals('Test User'));
      });

      test('should return email prefix when full name is empty', () {
        final user = validUser.copyWith(fullName: '');
        expect(user.displayName, equals('test'));
      });
    });

    group('hasCompleteProfile getter', () {
      test('should return true for complete profile', () {
        expect(validUser.hasCompleteProfile, isTrue);
      });

      test('should return false when full name is empty', () {
        final user = validUser.copyWith(fullName: '');
        expect(user.hasCompleteProfile, isFalse);
      });

      test('should return false when campus is null', () {
        final user = validUser.copyWith(campus: null);
        expect(user.hasCompleteProfile, isFalse);
      });

      test('should return false when campus is empty', () {
        final user = validUser.copyWith(campus: '');
        expect(user.hasCompleteProfile, isFalse);
      });

      test('should return false when phone number is null', () {
        final user = validUser.copyWith(phoneNumber: null);
        expect(user.hasCompleteProfile, isFalse);
      });

      test('should return false when phone number is empty', () {
        final user = validUser.copyWith(phoneNumber: '');
        expect(user.hasCompleteProfile, isFalse);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        final json = validUser.toJson();
        
        expect(json['id'], equals('test-user-id'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['fullName'], equals('Test User'));
        expect(json['phoneNumber'], equals('+2348012345678'));
        expect(json['campus'], equals('University of Lagos'));
        expect(json['isVerified'], equals(true));
        expect(json['role'], equals('student'));
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'test-user-id',
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'phoneNumber': '+2348012345678',
          'campus': 'University of Lagos',
          'profileImageUrl': 'https://example.com/profile.jpg',
          'createdAt': testDate.toIso8601String(),
          'lastLoginAt': testDate.toIso8601String(),
          'isVerified': true,
          'role': 'student',
          'preferences': {
            'maxBudget': 50000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };

        final user = UserModel.fromJson(json);
        
        expect(user.id, equals('test-user-id'));
        expect(user.email, equals('<EMAIL>'));
        expect(user.fullName, equals('Test User'));
        expect(user.phoneNumber, equals('+2348012345678'));
        expect(user.campus, equals('University of Lagos'));
        expect(user.isVerified, equals(true));
        expect(user.role, equals(UserRole.student));
      });
    });
  });

  group('UserPreferences', () {
    test('should create with default values', () {
      const preferences = UserPreferences();
      
      expect(preferences.maxBudget, equals(50000.0));
      expect(preferences.preferredAmenities, isEmpty);
      expect(preferences.preferredType, equals(AccommodationType.any));
      expect(preferences.maxDistanceFromCampus, equals(5.0));
      expect(preferences.receiveNotifications, isTrue);
      expect(preferences.preferredCurrency, equals('NGN'));
    });

    test('should serialize and deserialize correctly', () {
      const preferences = UserPreferences(
        maxBudget: 75000.0,
        preferredAmenities: ['WiFi', 'Security'],
        preferredType: AccommodationType.apartment,
        maxDistanceFromCampus: 10.0,
        receiveNotifications: false,
        preferredCurrency: 'USD',
      );

      final json = preferences.toJson();
      final deserialized = UserPreferences.fromJson(json);

      expect(deserialized.maxBudget, equals(75000.0));
      expect(deserialized.preferredAmenities, equals(['WiFi', 'Security']));
      expect(deserialized.preferredType, equals(AccommodationType.apartment));
      expect(deserialized.maxDistanceFromCampus, equals(10.0));
      expect(deserialized.receiveNotifications, isFalse);
      expect(deserialized.preferredCurrency, equals('USD'));
    });
  });

  group('UserRole', () {
    test('should have correct enum values', () {
      expect(UserRole.values, hasLength(3));
      expect(UserRole.values, contains(UserRole.student));
      expect(UserRole.values, contains(UserRole.campusRep));
      expect(UserRole.values, contains(UserRole.admin));
    });
  });
}