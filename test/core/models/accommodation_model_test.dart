import 'package:flutter_test/flutter_test.dart';
import 'package:campus_find/core/models/accommodation_model.dart';

void main() {
  group('AccommodationModel', () {
    late Map<String, dynamic> validJson;
    late AccommodationModel validAccommodation;

    setUp(() {
      validJson = {
        'id': 'test-id-123',
        'title': 'Cozy Student Apartment',
        'description': 'A comfortable 2-bedroom apartment near campus',
        'type': 'apartment',
        'location': {
          'latitude': 6.5244,
          'longitude': 3.3792,
          'address': '123 University Road, Lagos',
          'city': 'Lagos',
          'state': 'Lagos',
          'country': 'Nigeria',
          'nearestCampus': 'University of Lagos',
          'distanceFromCampus': 2.5,
        },
        'pricing': {
          'monthly': 50000.0,
          'yearly': 600000.0,
          'securityDeposit': 100000.0,
          'agentFee': 25000.0,
          'currency': 'NGN',
          'negotiable': true,
          'paymentTerms': 'Monthly in advance',
        },
        'amenities': ['WiFi', 'Security', 'Parking', 'Generator'],
        'imageUrls': [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg',
        ],
        'contact': {
          'name': '<PERSON> Doe',
          'phone': '+2348012345678',
          'email': '<EMAIL>',
          'whatsapp': '+2348012345678',
          'availableHours': ['9:00 AM - 6:00 PM'],
        },
        'rating': 4.5,
        'reviewCount': 12,
        'isVerified': true,
        'isAvailable': true,
        'createdAt': '2024-01-15T10:30:00.000Z',
        'updatedAt': '2024-01-16T14:20:00.000Z',
        'ownerId': 'owner-123',
        'metadata': {'featured': true},
      };

      validAccommodation = AccommodationModel(
        id: 'test-id-123',
        title: 'Cozy Student Apartment',
        description: 'A comfortable 2-bedroom apartment near campus',
        type: AccommodationType.apartment,
        location: const LocationInfo(
          latitude: 6.5244,
          longitude: 3.3792,
          address: '123 University Road, Lagos',
          city: 'Lagos',
          state: 'Lagos',
          country: 'Nigeria',
          nearestCampus: 'University of Lagos',
          distanceFromCampus: 2.5,
        ),
        pricing: const PricingInfo(
          monthly: 50000.0,
          yearly: 600000.0,
          securityDeposit: 100000.0,
          agentFee: 25000.0,
          currency: 'NGN',
          negotiable: true,
          paymentTerms: 'Monthly in advance',
        ),
        amenities: const ['WiFi', 'Security', 'Parking', 'Generator'],
        imageUrls: const [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg',
        ],
        contact: const ContactInfo(
          name: 'John Doe',
          phone: '+2348012345678',
          email: '<EMAIL>',
          whatsapp: '+2348012345678',
          availableHours: ['9:00 AM - 6:00 PM'],
        ),
        rating: 4.5,
        reviewCount: 12,
        isVerified: true,
        isAvailable: true,
        createdAt: DateTime.parse('2024-01-15T10:30:00.000Z'),
        updatedAt: DateTime.parse('2024-01-16T14:20:00.000Z'),
        ownerId: 'owner-123',
        metadata: const {'featured': true},
      );
    });

    group('fromJson', () {
      test('should create AccommodationModel from valid JSON', () {
        final accommodation = AccommodationModel.fromJson(validJson);

        expect(accommodation.id, equals('test-id-123'));
        expect(accommodation.title, equals('Cozy Student Apartment'));
        expect(accommodation.description, equals('A comfortable 2-bedroom apartment near campus'));
        expect(accommodation.type, equals(AccommodationType.apartment));
        expect(accommodation.rating, equals(4.5));
        expect(accommodation.reviewCount, equals(12));
        expect(accommodation.isVerified, isTrue);
        expect(accommodation.isAvailable, isTrue);
        expect(accommodation.amenities, contains('WiFi'));
        expect(accommodation.imageUrls, hasLength(2));
      });

      test('should handle missing optional fields', () {
        final minimalJson = {
          'id': 'test-id',
          'title': 'Test Accommodation',
          'description': 'Test description',
          'type': 'hostel',
          'location': {
            'latitude': 6.5244,
            'longitude': 3.3792,
            'address': '123 Test Street',
          },
          'pricing': {
            'monthly': 30000.0,
          },
          'contact': {
            'name': 'Test Contact',
            'phone': '+2348012345678',
          },
          'createdAt': '2024-01-15T10:30:00.000Z',
        };

        final accommodation = AccommodationModel.fromJson(minimalJson);

        expect(accommodation.id, equals('test-id'));
        expect(accommodation.amenities, isEmpty);
        expect(accommodation.imageUrls, isEmpty);
        expect(accommodation.rating, equals(0.0));
        expect(accommodation.reviewCount, equals(0));
        expect(accommodation.isVerified, isFalse);
        expect(accommodation.isAvailable, isTrue);
        expect(accommodation.updatedAt, isNull);
        expect(accommodation.ownerId, isNull);
        expect(accommodation.metadata, isEmpty);
      });

      test('should handle unknown accommodation type', () {
        final jsonWithUnknownType = Map<String, dynamic>.from(validJson);
        jsonWithUnknownType['type'] = 'unknown_type';

        final accommodation = AccommodationModel.fromJson(jsonWithUnknownType);

        expect(accommodation.type, equals(AccommodationType.any));
      });
    });

    group('toJson', () {
      test('should convert AccommodationModel to JSON', () {
        final json = validAccommodation.toJson();

        expect(json['id'], equals('test-id-123'));
        expect(json['title'], equals('Cozy Student Apartment'));
        expect(json['type'], equals('apartment'));
        expect(json['rating'], equals(4.5));
        expect(json['reviewCount'], equals(12));
        expect(json['isVerified'], isTrue);
        expect(json['isAvailable'], isTrue);
        expect(json['amenities'], contains('WiFi'));
        expect(json['imageUrls'], hasLength(2));
        expect(json['createdAt'], equals('2024-01-15T10:30:00.000Z'));
        expect(json['updatedAt'], equals('2024-01-16T14:20:00.000Z'));
      });

      test('should handle null updatedAt', () {
        final accommodationWithoutUpdate = AccommodationModel(
          id: 'test-id',
          title: 'Test',
          description: 'Test description',
          type: AccommodationType.hostel,
          location: const LocationInfo(
            latitude: 6.5244,
            longitude: 3.3792,
            address: '123 Test Street',
          ),
          pricing: const PricingInfo(monthly: 30000.0),
          contact: const ContactInfo(
            name: 'Test Contact',
            phone: '+2348012345678',
          ),
          createdAt: DateTime.parse('2024-01-15T10:30:00.000Z'),
        );

        final json = accommodationWithoutUpdate.toJson();

        expect(json['updatedAt'], isNull);
      });
    });
  });

  group('LocationInfo', () {
    test('should create LocationInfo from JSON', () {
      final json = {
        'latitude': 6.5244,
        'longitude': 3.3792,
        'address': '123 University Road, Lagos',
        'city': 'Lagos',
        'state': 'Lagos',
        'country': 'Nigeria',
        'nearestCampus': 'University of Lagos',
        'distanceFromCampus': 2.5,
      };

      final location = LocationInfo.fromJson(json);

      expect(location.latitude, equals(6.5244));
      expect(location.longitude, equals(3.3792));
      expect(location.address, equals('123 University Road, Lagos'));
      expect(location.city, equals('Lagos'));
      expect(location.state, equals('Lagos'));
      expect(location.country, equals('Nigeria'));
      expect(location.nearestCampus, equals('University of Lagos'));
      expect(location.distanceFromCampus, equals(2.5));
    });

    test('should get LatLng correctly', () {
      const location = LocationInfo(
        latitude: 6.5244,
        longitude: 3.3792,
        address: '123 Test Street',
      );

      final latLng = location.latLng;

      expect(latLng.latitude, equals(6.5244));
      expect(latLng.longitude, equals(3.3792));
    });

    test('should convert to JSON', () {
      const location = LocationInfo(
        latitude: 6.5244,
        longitude: 3.3792,
        address: '123 University Road, Lagos',
        city: 'Lagos',
        state: 'Lagos',
        country: 'Nigeria',
        nearestCampus: 'University of Lagos',
        distanceFromCampus: 2.5,
      );

      final json = location.toJson();

      expect(json['latitude'], equals(6.5244));
      expect(json['longitude'], equals(3.3792));
      expect(json['address'], equals('123 University Road, Lagos'));
      expect(json['city'], equals('Lagos'));
      expect(json['distanceFromCampus'], equals(2.5));
    });
  });

  group('PricingInfo', () {
    test('should create PricingInfo from JSON', () {
      final json = {
        'monthly': 50000.0,
        'yearly': 600000.0,
        'securityDeposit': 100000.0,
        'agentFee': 25000.0,
        'currency': 'NGN',
        'negotiable': true,
        'paymentTerms': 'Monthly in advance',
      };

      final pricing = PricingInfo.fromJson(json);

      expect(pricing.monthly, equals(50000.0));
      expect(pricing.yearly, equals(600000.0));
      expect(pricing.securityDeposit, equals(100000.0));
      expect(pricing.agentFee, equals(25000.0));
      expect(pricing.currency, equals('NGN'));
      expect(pricing.negotiable, isTrue);
      expect(pricing.paymentTerms, equals('Monthly in advance'));
    });

    test('should handle default values', () {
      final json = {
        'monthly': 30000.0,
      };

      final pricing = PricingInfo.fromJson(json);

      expect(pricing.monthly, equals(30000.0));
      expect(pricing.yearly, isNull);
      expect(pricing.securityDeposit, isNull);
      expect(pricing.agentFee, isNull);
      expect(pricing.currency, equals('NGN'));
      expect(pricing.negotiable, isFalse);
      expect(pricing.paymentTerms, isNull);
    });

    test('should convert to JSON', () {
      const pricing = PricingInfo(
        monthly: 50000.0,
        yearly: 600000.0,
        securityDeposit: 100000.0,
        agentFee: 25000.0,
        currency: 'NGN',
        negotiable: true,
        paymentTerms: 'Monthly in advance',
      );

      final json = pricing.toJson();

      expect(json['monthly'], equals(50000.0));
      expect(json['yearly'], equals(600000.0));
      expect(json['securityDeposit'], equals(100000.0));
      expect(json['agentFee'], equals(25000.0));
      expect(json['currency'], equals('NGN'));
      expect(json['negotiable'], isTrue);
      expect(json['paymentTerms'], equals('Monthly in advance'));
    });
  });

  group('ContactInfo', () {
    test('should create ContactInfo from JSON', () {
      final json = {
        'name': 'John Doe',
        'phone': '+2348012345678',
        'email': '<EMAIL>',
        'whatsapp': '+2348012345678',
        'availableHours': ['9:00 AM - 6:00 PM'],
      };

      final contact = ContactInfo.fromJson(json);

      expect(contact.name, equals('John Doe'));
      expect(contact.phone, equals('+2348012345678'));
      expect(contact.email, equals('<EMAIL>'));
      expect(contact.whatsapp, equals('+2348012345678'));
      expect(contact.availableHours, contains('9:00 AM - 6:00 PM'));
    });

    test('should handle minimal required fields', () {
      final json = {
        'name': 'Jane Doe',
        'phone': '+2348087654321',
      };

      final contact = ContactInfo.fromJson(json);

      expect(contact.name, equals('Jane Doe'));
      expect(contact.phone, equals('+2348087654321'));
      expect(contact.email, isNull);
      expect(contact.whatsapp, isNull);
      expect(contact.availableHours, isEmpty);
    });

    test('should convert to JSON', () {
      const contact = ContactInfo(
        name: 'John Doe',
        phone: '+2348012345678',
        email: '<EMAIL>',
        whatsapp: '+2348012345678',
        availableHours: ['9:00 AM - 6:00 PM'],
      );

      final json = contact.toJson();

      expect(json['name'], equals('John Doe'));
      expect(json['phone'], equals('+2348012345678'));
      expect(json['email'], equals('<EMAIL>'));
      expect(json['whatsapp'], equals('+2348012345678'));
      expect(json['availableHours'], contains('9:00 AM - 6:00 PM'));
    });
  });

  group('AccommodationType', () {
    test('should have all expected types', () {
      expect(AccommodationType.values, contains(AccommodationType.hostel));
      expect(AccommodationType.values, contains(AccommodationType.apartment));
      expect(AccommodationType.values, contains(AccommodationType.sharedRoom));
      expect(AccommodationType.values, contains(AccommodationType.singleRoom));
      expect(AccommodationType.values, contains(AccommodationType.studio));
      expect(AccommodationType.values, contains(AccommodationType.any));
    });
  });
}