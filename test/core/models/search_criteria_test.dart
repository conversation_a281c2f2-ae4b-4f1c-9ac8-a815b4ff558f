import 'package:flutter_test/flutter_test.dart';
import 'package:campus_find/core/models/search_criteria.dart';
import 'package:campus_find/core/models/accommodation_model.dart';

void main() {
  group('SearchCriteria', () {
    test('should create SearchCriteria with default values', () {
      const criteria = SearchCriteria();

      expect(criteria.query, isNull);
      expect(criteria.location, isNull);
      expect(criteria.minPrice, isNull);
      expect(criteria.maxPrice, isNull);
      expect(criteria.amenities, isEmpty);
      expect(criteria.type, isNull);
      expect(criteria.maxDistanceFromCampus, isNull);
      expect(criteria.isVerified, isNull);
      expect(criteria.isAvailable, isNull);
      expect(criteria.sortBy, SortOption.relevance);
      expect(criteria.sortOrder, SortOrder.descending);
      expect(criteria.limit, 20);
      expect(criteria.offset, 0);
    });

    test('should create SearchCriteria with custom values', () {
      const criteria = SearchCriteria(
        query: 'student accommodation',
        location: 'Lagos',
        minPrice: 10000,
        maxPrice: 50000,
        amenities: ['WiFi', 'Security'],
        type: AccommodationType.hostel,
        maxDistanceFromCampus: 5.0,
        isVerified: true,
        isAvailable: true,
        sortBy: SortOption.price,
        sortOrder: SortOrder.ascending,
        limit: 10,
        offset: 5,
      );

      expect(criteria.query, 'student accommodation');
      expect(criteria.location, 'Lagos');
      expect(criteria.minPrice, 10000);
      expect(criteria.maxPrice, 50000);
      expect(criteria.amenities, ['WiFi', 'Security']);
      expect(criteria.type, AccommodationType.hostel);
      expect(criteria.maxDistanceFromCampus, 5.0);
      expect(criteria.isVerified, true);
      expect(criteria.isAvailable, true);
      expect(criteria.sortBy, SortOption.price);
      expect(criteria.sortOrder, SortOrder.ascending);
      expect(criteria.limit, 10);
      expect(criteria.offset, 5);
    });

    test('should create copy with modified values', () {
      const original = SearchCriteria(
        query: 'original query',
        location: 'Lagos',
        minPrice: 10000,
      );

      final copy = original.copyWith(
        query: 'new query',
        maxPrice: 50000,
      );

      expect(copy.query, 'new query');
      expect(copy.location, 'Lagos'); // unchanged
      expect(copy.minPrice, 10000); // unchanged
      expect(copy.maxPrice, 50000); // new value
    });

    test('should serialize to and from JSON correctly', () {
      const original = SearchCriteria(
        query: 'test query',
        location: 'Lagos',
        minPrice: 10000,
        maxPrice: 50000,
        amenities: ['WiFi', 'Security'],
        type: AccommodationType.hostel,
        maxDistanceFromCampus: 5.0,
        isVerified: true,
        isAvailable: true,
        sortBy: SortOption.price,
        sortOrder: SortOrder.ascending,
        limit: 10,
        offset: 5,
      );

      final json = original.toJson();
      final fromJson = SearchCriteria.fromJson(json);

      expect(fromJson.query, original.query);
      expect(fromJson.location, original.location);
      expect(fromJson.minPrice, original.minPrice);
      expect(fromJson.maxPrice, original.maxPrice);
      expect(fromJson.amenities, original.amenities);
      expect(fromJson.type, original.type);
      expect(fromJson.maxDistanceFromCampus, original.maxDistanceFromCampus);
      expect(fromJson.isVerified, original.isVerified);
      expect(fromJson.isAvailable, original.isAvailable);
      expect(fromJson.sortBy, original.sortBy);
      expect(fromJson.sortOrder, original.sortOrder);
      expect(fromJson.limit, original.limit);
      expect(fromJson.offset, original.offset);
    });

    test('should handle equality correctly', () {
      const criteria1 = SearchCriteria(
        query: 'test',
        location: 'Lagos',
        amenities: ['WiFi'],
      );

      const criteria2 = SearchCriteria(
        query: 'test',
        location: 'Lagos',
        amenities: ['WiFi'],
      );

      const criteria3 = SearchCriteria(
        query: 'different',
        location: 'Lagos',
        amenities: ['WiFi'],
      );

      expect(criteria1, equals(criteria2));
      expect(criteria1, isNot(equals(criteria3)));
    });
  });

  group('SearchResult', () {
    test('should create SearchResult correctly', () {
      final accommodations = <AccommodationModel>[];
      const criteria = SearchCriteria();
      
      const result = SearchResult(
        accommodations: [],
        totalCount: 0,
        hasMore: false,
        criteria: criteria,
      );

      expect(result.accommodations, isEmpty);
      expect(result.totalCount, 0);
      expect(result.hasMore, false);
      expect(result.criteria, criteria);
    });

    test('should create copy with modified values', () {
      const original = SearchResult(
        accommodations: [],
        totalCount: 10,
        hasMore: true,
        criteria: SearchCriteria(),
      );

      final copy = original.copyWith(
        totalCount: 20,
        hasMore: false,
      );

      expect(copy.totalCount, 20);
      expect(copy.hasMore, false);
      expect(copy.accommodations, original.accommodations);
      expect(copy.criteria, original.criteria);
    });
  });

  group('FilterOptions', () {
    test('should serialize to and from JSON correctly', () {
      const original = FilterOptions(
        availableAmenities: ['WiFi', 'Security', 'Parking'],
        availableTypes: [AccommodationType.hostel, AccommodationType.apartment],
        priceRange: PriceRange(min: 5000, max: 100000),
        availableLocations: ['Lagos', 'Abuja', 'Ibadan'],
      );

      final json = original.toJson();
      final fromJson = FilterOptions.fromJson(json);

      expect(fromJson.availableAmenities, original.availableAmenities);
      expect(fromJson.availableTypes, original.availableTypes);
      expect(fromJson.priceRange.min, original.priceRange.min);
      expect(fromJson.priceRange.max, original.priceRange.max);
      expect(fromJson.availableLocations, original.availableLocations);
    });
  });

  group('PriceRange', () {
    test('should create PriceRange correctly', () {
      const priceRange = PriceRange(min: 10000, max: 50000);

      expect(priceRange.min, 10000);
      expect(priceRange.max, 50000);
    });

    test('should serialize to and from JSON correctly', () {
      const original = PriceRange(min: 15000, max: 75000);

      final json = original.toJson();
      final fromJson = PriceRange.fromJson(json);

      expect(fromJson.min, original.min);
      expect(fromJson.max, original.max);
    });
  });

  group('Enums', () {
    test('SortOption should have all expected values', () {
      expect(SortOption.values, [
        SortOption.relevance,
        SortOption.price,
        SortOption.rating,
        SortOption.distance,
        SortOption.newest,
        SortOption.oldest,
      ]);
    });

    test('SortOrder should have all expected values', () {
      expect(SortOrder.values, [
        SortOrder.ascending,
        SortOrder.descending,
      ]);
    });
  });
}