import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:campus_find/core/services/auth_service.dart';
import 'package:campus_find/core/models/user_model.dart';

import 'auth_service_test.mocks.dart';

@GenerateMocks([
  FirebaseAuth,
  FirebaseFirestore,
  User,
  UserCredential,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
])
void main() {
  group('AuthService', () {
    late AuthService authService;
    late MockFirebaseAuth mockAuth;
    late MockFirebaseFirestore mockFirestore;
    late MockUser mockUser;
    late MockUserCredential mockUserCredential;
    late MockCollectionReference<Map<String, dynamic>> mockCollection;
    late MockDocumentReference<Map<String, dynamic>> mockDocument;
    late MockDocumentSnapshot<Map<String, dynamic>> mockDocumentSnapshot;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirebaseFirestore();
      mockUser = MockUser();
      mockUserCredential = MockUserCredential();
      mockCollection = MockCollectionReference<Map<String, dynamic>>();
      mockDocument = MockDocumentReference<Map<String, dynamic>>();
      mockDocumentSnapshot = MockDocumentSnapshot<Map<String, dynamic>>();
      
      authService = AuthService(mockAuth, mockFirestore);

      // Setup common mocks
      when(mockFirestore.collection('users')).thenReturn(mockCollection);
      when(mockCollection.doc(any)).thenReturn(mockDocument);
      when(mockUserCredential.user).thenReturn(mockUser);
      when(mockUser.uid).thenReturn('test-uid');
      when(mockUser.email).thenReturn('<EMAIL>');
      when(mockUser.emailVerified).thenReturn(false);
    });

    group('currentUser', () {
      test('should return current user from FirebaseAuth', () {
        when(mockAuth.currentUser).thenReturn(mockUser);
        
        final result = authService.currentUser;
        
        expect(result, equals(mockUser));
        verify(mockAuth.currentUser).called(1);
      });

      test('should return null when no user is signed in', () {
        when(mockAuth.currentUser).thenReturn(null);
        
        final result = authService.currentUser;
        
        expect(result, isNull);
        verify(mockAuth.currentUser).called(1);
      });
    });

    group('authStateChanges', () {
      test('should return auth state changes stream', () {
        final stream = Stream<User?>.fromIterable([mockUser, null]);
        when(mockAuth.authStateChanges()).thenAnswer((_) => stream);
        
        final result = authService.authStateChanges;
        
        expect(result, equals(stream));
        verify(mockAuth.authStateChanges()).called(1);
      });
    });

    group('signInWithEmailAndPassword', () {
      test('should sign in successfully and update last login time', () async {
        when(mockAuth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
        )).thenAnswer((_) async => mockUserCredential);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        final result = await authService.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
        );

        expect(result, equals(mockUserCredential));
        verify(mockAuth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
        )).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should handle FirebaseAuthException', () async {
        when(mockAuth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'wrongpassword',
        )).thenThrow(FirebaseAuthException(code: 'wrong-password'));

        expect(
          () => authService.signInWithEmailAndPassword(
            email: '<EMAIL>',
            password: 'wrongpassword',
          ),
          throwsA(equals('Incorrect password.')),
        );
      });
    });

    group('createUserWithEmailAndPassword', () {
      test('should create user successfully and send verification email', () async {
        when(mockAuth.createUserWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
        )).thenAnswer((_) async => mockUserCredential);
        when(mockDocument.set(any)).thenAnswer((_) async => {});
        when(mockUser.sendEmailVerification()).thenAnswer((_) async => {});

        final result = await authService.createUserWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
          fullName: 'Test User',
          phoneNumber: '+2348012345678',
          campus: 'University of Lagos',
        );

        expect(result, equals(mockUserCredential));
        verify(mockAuth.createUserWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password123',
        )).called(1);
        verify(mockDocument.set(any)).called(1);
        verify(mockUser.sendEmailVerification()).called(1);
      });

      test('should handle FirebaseAuthException during registration', () async {
        when(mockAuth.createUserWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'weak',
        )).thenThrow(FirebaseAuthException(code: 'weak-password'));

        expect(
          () => authService.createUserWithEmailAndPassword(
            email: '<EMAIL>',
            password: 'weak',
            fullName: 'Test User',
          ),
          throwsA(equals('Password is too weak.')),
        );
      });
    });

    group('signOut', () {
      test('should sign out successfully', () async {
        when(mockAuth.signOut()).thenAnswer((_) async => {});

        await authService.signOut();

        verify(mockAuth.signOut()).called(1);
      });
    });

    group('sendPasswordResetEmail', () {
      test('should send password reset email successfully', () async {
        when(mockAuth.sendPasswordResetEmail(email: '<EMAIL>'))
            .thenAnswer((_) async => {});

        await authService.sendPasswordResetEmail('<EMAIL>');

        verify(mockAuth.sendPasswordResetEmail(email: '<EMAIL>'))
            .called(1);
      });

      test('should handle FirebaseAuthException', () async {
        when(mockAuth.sendPasswordResetEmail(email: '<EMAIL>'))
            .thenThrow(FirebaseAuthException(code: 'user-not-found'));

        expect(
          () => authService.sendPasswordResetEmail('<EMAIL>'),
          throwsA(equals('No user found with this email address.')),
        );
      });
    });

    group('sendEmailVerification', () {
      test('should send email verification when user exists and not verified', () async {
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(false);
        when(mockUser.sendEmailVerification()).thenAnswer((_) async => {});

        await authService.sendEmailVerification();

        verify(mockUser.sendEmailVerification()).called(1);
      });

      test('should not send verification when user is already verified', () async {
        when(mockAuth.currentUser).thenReturn(mockUser);
        when(mockUser.emailVerified).thenReturn(true);

        await authService.sendEmailVerification();

        verifyNever(mockUser.sendEmailVerification());
      });

      test('should not send verification when no user is signed in', () async {
        when(mockAuth.currentUser).thenReturn(null);

        await authService.sendEmailVerification();

        verifyNever(mockUser.sendEmailVerification());
      });
    });

    group('getUserProfile', () {
      test('should return user profile when document exists', () async {
        final userData = {
          'id': 'test-uid',
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'createdAt': DateTime.now().toIso8601String(),
          'preferences': {
            'maxBudget': 50000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };
        
        when(mockDocument.get()).thenAnswer((_) async => mockDocumentSnapshot);
        when(mockDocumentSnapshot.exists).thenReturn(true);
        when(mockDocumentSnapshot.data()).thenReturn(userData);

        final result = await authService.getUserProfile('test-uid');

        expect(result, isA<UserModel>());
        expect(result?.id, equals('test-uid'));
        expect(result?.email, equals('<EMAIL>'));
        verify(mockDocument.get()).called(1);
      });

      test('should return null when document does not exist', () async {
        when(mockDocument.get()).thenAnswer((_) async => mockDocumentSnapshot);
        when(mockDocumentSnapshot.exists).thenReturn(false);

        final result = await authService.getUserProfile('test-uid');

        expect(result, isNull);
        verify(mockDocument.get()).called(1);
      });

      test('should return null when exception occurs', () async {
        when(mockDocument.get()).thenThrow(Exception('Firestore error'));

        final result = await authService.getUserProfile('test-uid');

        expect(result, isNull);
        verify(mockDocument.get()).called(1);
      });
    });

    group('updateUserProfile', () {
      test('should update user profile successfully', () async {
        final updateData = {'fullName': 'Updated Name'};
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        await authService.updateUserProfile('test-uid', updateData);

        verify(mockDocument.update(any)).called(1);
      });

      test('should rethrow exception when update fails', () async {
        final updateData = {'fullName': 'Updated Name'};
        when(mockDocument.update(any)).thenThrow(Exception('Update failed'));

        expect(
          () => authService.updateUserProfile('test-uid', updateData),
          throwsException,
        );
      });
    });

    group('_handleAuthException', () {
      test('should return correct error messages for known error codes', () async {
        final testCases = {
          'user-not-found': 'No user found with this email address.',
          'wrong-password': 'Incorrect password.',
          'email-already-in-use': 'An account already exists with this email address.',
          'weak-password': 'Password is too weak.',
          'invalid-email': 'Invalid email address.',
          'user-disabled': 'This account has been disabled.',
          'too-many-requests': 'Too many failed attempts. Please try again later.',
        };

        for (final entry in testCases.entries) {
          when(mockAuth.signInWithEmailAndPassword(
            email: '<EMAIL>',
            password: 'password',
          )).thenThrow(FirebaseAuthException(code: entry.key));
          
          expect(
            () => authService.signInWithEmailAndPassword(
              email: '<EMAIL>',
              password: 'password',
            ),
            throwsA(equals(entry.value)),
          );
        }
      });

      test('should return default message for unknown error codes', () async {
        when(mockAuth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'password',
        )).thenThrow(FirebaseAuthException(
          code: 'unknown-error',
          message: 'Custom error message',
        ));

        expect(
          () => authService.signInWithEmailAndPassword(
            email: '<EMAIL>',
            password: 'password',
          ),
          throwsA(equals('Custom error message')),
        );
      });
    });
  });
}