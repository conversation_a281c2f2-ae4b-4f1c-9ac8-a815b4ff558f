import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:campus_find/features/hostels/data/accommodation_repository.dart';
import 'package:campus_find/core/models/accommodation_model.dart';
import 'package:campus_find/core/models/search_criteria.dart';

import 'accommodation_repository_test.mocks.dart';

@GenerateMocks([
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  QuerySnapshot,
  Query,
])
void main() {
  group('AccommodationRepository', () {
    late AccommodationRepository repository;
    late MockFirebaseFirestore mockFirestore;
    late MockCollectionReference<Map<String, dynamic>> mockCollection;
    late MockDocumentReference<Map<String, dynamic>> mockDocument;
    late MockDocumentSnapshot<Map<String, dynamic>> mockDocumentSnapshot;
    late MockQuerySnapshot<Map<String, dynamic>> mockQuerySnapshot;
    late MockQuery<Map<String, dynamic>> mockQuery;

    setUp(() {
      mockFirestore = MockFirebaseFirestore();
      mockCollection = MockCollectionReference<Map<String, dynamic>>();
      mockDocument = MockDocumentReference<Map<String, dynamic>>();
      mockDocumentSnapshot = MockDocumentSnapshot<Map<String, dynamic>>();
      mockQuerySnapshot = MockQuerySnapshot<Map<String, dynamic>>();
      mockQuery = MockQuery<Map<String, dynamic>>();

      repository = AccommodationRepository(firestore: mockFirestore);

      when(mockFirestore.collection('accommodations')).thenReturn(mockCollection);
    });

    group('create', () {
      test('should create accommodation successfully', () async {
        final accommodation = _createTestAccommodation();
        
        when(mockCollection.doc(accommodation.id)).thenReturn(mockDocument);
        when(mockDocument.set(any)).thenAnswer((_) async {});

        await repository.create(accommodation);

        verify(mockCollection.doc(accommodation.id)).called(1);
        verify(mockDocument.set(accommodation.toJson())).called(1);
      });

      test('should throw AccommodationRepositoryException on error', () async {
        final accommodation = _createTestAccommodation();
        
        when(mockCollection.doc(accommodation.id)).thenReturn(mockDocument);
        when(mockDocument.set(any)).thenThrow(Exception('Firestore error'));

        expect(
          () => repository.create(accommodation),
          throwsA(isA<AccommodationRepositoryException>()),
        );
      });
    });

    group('getById', () {
      test('should return accommodation when document exists', () async {
        const accommodationId = 'test-id';
        final accommodationData = _createTestAccommodationData();
        
        when(mockCollection.doc(accommodationId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async => mockDocumentSnapshot);
        when(mockDocumentSnapshot.exists).thenReturn(true);
        when(mockDocumentSnapshot.data()).thenReturn(accommodationData);
        when(mockDocumentSnapshot.id).thenReturn(accommodationId);

        final result = await repository.getById(accommodationId);

        expect(result, isNotNull);
        expect(result!.id, equals(accommodationId));
        expect(result.title, equals(accommodationData['title']));
      });

      test('should return null when document does not exist', () async {
        const accommodationId = 'non-existent-id';
        
        when(mockCollection.doc(accommodationId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async => mockDocumentSnapshot);
        when(mockDocumentSnapshot.exists).thenReturn(false);

        final result = await repository.getById(accommodationId);

        expect(result, isNull);
      });

      test('should throw AccommodationRepositoryException on error', () async {
        const accommodationId = 'test-id';
        
        when(mockCollection.doc(accommodationId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenThrow(Exception('Firestore error'));

        expect(
          () => repository.getById(accommodationId),
          throwsA(isA<AccommodationRepositoryException>()),
        );
      });
    });

    group('update', () {
      test('should update accommodation successfully', () async {
        final accommodation = _createTestAccommodation();
        
        when(mockCollection.doc(accommodation.id)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async {});

        await repository.update(accommodation);

        verify(mockCollection.doc(accommodation.id)).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should throw AccommodationRepositoryException on error', () async {
        final accommodation = _createTestAccommodation();
        
        when(mockCollection.doc(accommodation.id)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenThrow(Exception('Firestore error'));

        expect(
          () => repository.update(accommodation),
          throwsA(isA<AccommodationRepositoryException>()),
        );
      });
    });

    group('delete', () {
      test('should delete accommodation successfully', () async {
        const accommodationId = 'test-id';
        
        when(mockCollection.doc(accommodationId)).thenReturn(mockDocument);
        when(mockDocument.delete()).thenAnswer((_) async {});

        await repository.delete(accommodationId);

        verify(mockCollection.doc(accommodationId)).called(1);
        verify(mockDocument.delete()).called(1);
      });

      test('should throw AccommodationRepositoryException on error', () async {
        const accommodationId = 'test-id';
        
        when(mockCollection.doc(accommodationId)).thenReturn(mockDocument);
        when(mockDocument.delete()).thenThrow(Exception('Firestore error'));

        expect(
          () => repository.delete(accommodationId),
          throwsA(isA<AccommodationRepositoryException>()),
        );
      });
    });

    group('search', () {
      test('should return search results with accommodations', () async {
        const criteria = SearchCriteria(
          query: 'student apartment',
          location: 'Lagos',
          minPrice: 20000,
          maxPrice: 80000,
          limit: 10,
        );

        final accommodationData = _createTestAccommodationData();
        final mockQueryDocumentSnapshot = MockQueryDocumentSnapshot<Map<String, dynamic>>();
        
        when(mockCollection.where(any, isEqualTo: anyNamed('isEqualTo')))
            .thenReturn(mockQuery);
        when(mockCollection.where(any, isGreaterThanOrEqualTo: anyNamed('isGreaterThanOrEqualTo')))
            .thenReturn(mockQuery);
        when(mockCollection.where(any, isLessThanOrEqualTo: anyNamed('isLessThanOrEqualTo')))
            .thenReturn(mockQuery);
        when(mockQuery.where(any, isEqualTo: anyNamed('isEqualTo')))
            .thenReturn(mockQuery);
        when(mockQuery.where(any, isGreaterThanOrEqualTo: anyNamed('isGreaterThanOrEqualTo')))
            .thenReturn(mockQuery);
        when(mockQuery.where(any, isLessThanOrEqualTo: anyNamed('isLessThanOrEqualTo')))
            .thenReturn(mockQuery);
        when(mockQuery.orderBy(any, descending: anyNamed('descending')))
            .thenReturn(mockQuery);
        when(mockQuery.limit(any)).thenReturn(mockQuery);
        when(mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
        
        when(mockQuerySnapshot.docs).thenReturn([mockQueryDocumentSnapshot]);
        when(mockQueryDocumentSnapshot.data()).thenReturn(accommodationData);
        when(mockQueryDocumentSnapshot.id).thenReturn('test-id');

        // Mock total count query
        when(mockCollection.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([mockQueryDocumentSnapshot]);

        final result = await repository.search(criteria);

        expect(result.accommodations, hasLength(1));
        expect(result.accommodations.first.id, equals('test-id'));
        expect(result.criteria, equals(criteria));
      });

      test('should throw AccommodationRepositoryException on error', () async {
        const criteria = SearchCriteria();
        
        when(mockCollection.where(any, isEqualTo: anyNamed('isEqualTo')))
            .thenThrow(Exception('Firestore error'));

        expect(
          () => repository.search(criteria),
          throwsA(isA<AccommodationRepositoryException>()),
        );
      });
    });

    group('getFilterOptions', () {
      test('should return filter options from accommodations', () async {
        final accommodationData1 = _createTestAccommodationData();
        final accommodationData2 = Map<String, dynamic>.from(accommodationData1);
        accommodationData2['pricing']['monthly'] = 30000.0;
        accommodationData2['amenities'] = ['WiFi', 'Parking'];
        accommodationData2['type'] = 'apartment';

        final mockDoc1 = MockQueryDocumentSnapshot<Map<String, dynamic>>();
        final mockDoc2 = MockQueryDocumentSnapshot<Map<String, dynamic>>();
        
        when(mockCollection.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([mockDoc1, mockDoc2]);
        
        when(mockDoc1.data()).thenReturn(accommodationData1);
        when(mockDoc1.id).thenReturn('id1');
        when(mockDoc2.data()).thenReturn(accommodationData2);
        when(mockDoc2.id).thenReturn('id2');

        final result = await repository.getFilterOptions();

        expect(result.availableAmenities, isNotEmpty);
        expect(result.availableTypes, isNotEmpty);
        expect(result.availableLocations, isNotEmpty);
        expect(result.priceRange.min, lessThanOrEqualTo(result.priceRange.max));
      });

      test('should throw AccommodationRepositoryException on error', () async {
        when(mockCollection.get()).thenThrow(Exception('Firestore error'));

        expect(
          () => repository.getFilterOptions(),
          throwsA(isA<AccommodationRepositoryException>()),
        );
      });
    });

    group('distance calculation', () {
      test('should calculate distance correctly', () async {
        // Test the distance calculation by calling getNearLocation
        final accommodationData = _createTestAccommodationData();
        final mockDoc = MockQueryDocumentSnapshot<Map<String, dynamic>>();
        
        when(mockCollection.where('isAvailable', isEqualTo: true))
            .thenReturn(mockQuery);
        when(mockQuery.limit(any)).thenReturn(mockQuery);
        when(mockQuery.get()).thenAnswer((_) async => mockQuerySnapshot);
        when(mockQuerySnapshot.docs).thenReturn([mockDoc]);
        when(mockDoc.data()).thenReturn(accommodationData);
        when(mockDoc.id).thenReturn('test-id');

        final result = await repository.getNearLocation(
          latitude: 6.5244,
          longitude: 3.3792,
          radiusInKm: 10.0,
        );

        expect(result, isNotEmpty);
      });
    });
  });
}

AccommodationModel _createTestAccommodation() {
  return AccommodationModel(
    id: 'test-id',
    title: 'Test Accommodation',
    description: 'A test accommodation',
    type: AccommodationType.hostel,
    location: const LocationInfo(
      latitude: 6.5244,
      longitude: 3.3792,
      address: '123 Test Street',
      city: 'Lagos',
    ),
    pricing: const PricingInfo(monthly: 50000.0),
    contact: const ContactInfo(
      name: 'Test Contact',
      phone: '+2348012345678',
    ),
    createdAt: DateTime.now(),
  );
}

Map<String, dynamic> _createTestAccommodationData() {
  return {
    'title': 'Test Accommodation',
    'description': 'A test accommodation',
    'type': 'hostel',
    'location': {
      'latitude': 6.5244,
      'longitude': 3.3792,
      'address': '123 Test Street',
      'city': 'Lagos',
      'state': 'Lagos',
      'country': 'Nigeria',
      'distanceFromCampus': 2.0,
    },
    'pricing': {
      'monthly': 50000.0,
      'currency': 'NGN',
      'negotiable': false,
    },
    'amenities': ['WiFi', 'Security'],
    'imageUrls': [],
    'contact': {
      'name': 'Test Contact',
      'phone': '+2348012345678',
      'availableHours': [],
    },
    'rating': 4.0,
    'reviewCount': 5,
    'isVerified': true,
    'isAvailable': true,
    'createdAt': DateTime.now().toIso8601String(),
    'metadata': {},
  };
}

// Mock class for QueryDocumentSnapshot
class MockQueryDocumentSnapshot<T> extends Mock implements QueryDocumentSnapshot<T> {}