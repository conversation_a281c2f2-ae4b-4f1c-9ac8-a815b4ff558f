import 'package:flutter_test/flutter_test.dart';
import 'package:campus_find/core/models/accommodation_model.dart';
import 'package:campus_find/core/models/search_criteria.dart';
import 'package:campus_find/features/hostels/data/search_engine.dart';
import 'package:campus_find/features/hostels/data/accommodation_repository.dart';

// Mock class for testing
class MockAccommodationRepository implements AccommodationRepository {
  @override
  Future<void> create(AccommodationModel accommodation) async {}

  @override
  Future<AccommodationModel?> getById(String id) async => null;

  @override
  Future<void> update(AccommodationModel accommodation) async {}

  @override
  Future<void> delete(String id) async {}

  @override
  Future<SearchResult> search(SearchCriteria criteria) async {
    return const SearchResult(
      accommodations: [],
      totalCount: 0,
      hasMore: false,
      criteria: SearchCriteria(),
    );
  }

  @override
  Future<List<AccommodationModel>> getAll({int limit = 20, startAfter}) async => [];

  @override
  Future<List<AccommodationModel>> getByOwnerId(String ownerId) async => [];

  @override
  Future<List<AccommodationModel>> getNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInKm,
    int limit = 20,
  }) async => [];

  @override
  Future<FilterOptions> getFilterOptions() async {
    return const FilterOptions(
      availableAmenities: [],
      availableTypes: [],
      priceRange: PriceRange(min: 0, max: 100000),
      availableLocations: [],
    );
  }
}

void main() {
  group('SearchEngine Core Functionality', () {
    late SearchEngine searchEngine;

    setUp(() {
      // Create SearchEngine with a mock repository to avoid Firebase initialization
      final mockRepository = MockAccommodationRepository();
      searchEngine = SearchEngine(repository: mockRepository);
    });

    group('Distance Calculations', () {
      test('should calculate distance between two points correctly', () {
        // Arrange - University of Lagos coordinates
        const lat1 = 6.5244;
        const lon1 = 3.3792;
        const lat2 = 6.5200; // Nearby location
        const lon2 = 3.3800;

        // Act
        final distance = searchEngine.calculateDistance(lat1, lon1, lat2, lon2);

        // Assert
        expect(distance, greaterThan(0));
        expect(distance, lessThan(1)); // Should be less than 1km
      });

      test('should return zero for identical coordinates', () {
        // Arrange
        const lat = 6.5244;
        const lon = 3.3792;

        // Act
        final distance = searchEngine.calculateDistance(lat, lon, lat, lon);

        // Assert
        expect(distance, equals(0));
      });

      test('should calculate longer distances correctly', () {
        // Arrange - Lagos to Abuja (approximate)
        const lagosLat = 6.5244;
        const lagosLon = 3.3792;
        const abujaLat = 9.0765;
        const abujaLon = 7.3986;

        // Act
        final distance = searchEngine.calculateDistance(lagosLat, lagosLon, abujaLat, abujaLon);

        // Assert
        expect(distance, greaterThan(400)); // Should be around 400-500km
        expect(distance, lessThan(600));
      });
    });

    group('Price Filtering', () {
      test('should filter accommodations by minimum price', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', price: 80000),
          _createTestAccommodation(id: '2', price: 30000),
          _createTestAccommodation(id: '3', price: 50000),
        ];

        // Act
        final result = searchEngine.filterByPrice(
          accommodations: accommodations,
          minPrice: 40000,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.pricing.monthly >= 40000), isTrue);
      });

      test('should filter accommodations by maximum price', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', price: 80000),
          _createTestAccommodation(id: '2', price: 30000),
          _createTestAccommodation(id: '3', price: 50000),
        ];

        // Act
        final result = searchEngine.filterByPrice(
          accommodations: accommodations,
          maxPrice: 60000,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.pricing.monthly <= 60000), isTrue);
      });

      test('should filter accommodations by price range', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', price: 80000),
          _createTestAccommodation(id: '2', price: 30000),
          _createTestAccommodation(id: '3', price: 50000),
          _createTestAccommodation(id: '4', price: 45000),
        ];

        // Act
        final result = searchEngine.filterByPrice(
          accommodations: accommodations,
          minPrice: 40000,
          maxPrice: 60000,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => 
          acc.pricing.monthly >= 40000 && acc.pricing.monthly <= 60000), isTrue);
      });
    });

    group('Amenities Filtering', () {
      test('should filter accommodations by required amenities', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(
            id: '1', 
            amenities: ['WiFi', 'Security', 'Parking', 'Generator']
          ),
          _createTestAccommodation(
            id: '2', 
            amenities: ['WiFi', 'Security']
          ),
          _createTestAccommodation(
            id: '3', 
            amenities: ['WiFi', 'Parking']
          ),
        ];

        // Act
        final result = searchEngine.filterByAmenities(
          accommodations: accommodations,
          requiredAmenities: ['WiFi', 'Security'],
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => 
          acc.amenities.contains('WiFi') && acc.amenities.contains('Security')), isTrue);
      });

      test('should return all accommodations when no amenities required', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', amenities: ['WiFi']),
          _createTestAccommodation(id: '2', amenities: ['Security']),
        ];

        // Act
        final result = searchEngine.filterByAmenities(
          accommodations: accommodations,
          requiredAmenities: [],
        );

        // Assert
        expect(result.length, equals(2));
      });
    });

    group('Type Filtering', () {
      test('should filter accommodations by type', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', type: AccommodationType.apartment),
          _createTestAccommodation(id: '2', type: AccommodationType.singleRoom),
          _createTestAccommodation(id: '3', type: AccommodationType.apartment),
        ];

        // Act
        final result = searchEngine.filterByType(
          accommodations: accommodations,
          type: AccommodationType.apartment,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.type == AccommodationType.apartment), isTrue);
      });

      test('should return all accommodations for "any" type', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', type: AccommodationType.apartment),
          _createTestAccommodation(id: '2', type: AccommodationType.singleRoom),
        ];

        // Act
        final result = searchEngine.filterByType(
          accommodations: accommodations,
          type: AccommodationType.any,
        );

        // Assert
        expect(result.length, equals(2));
      });
    });

    group('Rating Filtering', () {
      test('should filter by minimum rating', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', rating: 4.5),
          _createTestAccommodation(id: '2', rating: 3.0),
          _createTestAccommodation(id: '3', rating: 2.5),
          _createTestAccommodation(id: '4', rating: 4.8),
        ];

        // Act
        final result = searchEngine.filterByRating(
          accommodations: accommodations,
          minRating: 4.0,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.rating >= 4.0), isTrue);
      });

      test('should filter by rating range', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', rating: 4.5),
          _createTestAccommodation(id: '2', rating: 3.0),
          _createTestAccommodation(id: '3', rating: 2.5),
          _createTestAccommodation(id: '4', rating: 4.8),
        ];

        // Act
        final result = searchEngine.filterByRating(
          accommodations: accommodations,
          minRating: 3.5,
          maxRating: 4.6,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.rating, equals(4.5));
      });
    });

    group('Availability and Verification Filtering', () {
      test('should filter by availability status', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', isAvailable: true),
          _createTestAccommodation(id: '2', isAvailable: false),
          _createTestAccommodation(id: '3', isAvailable: true),
        ];

        // Act
        final result = searchEngine.filterByAvailability(
          accommodations: accommodations,
          isAvailable: true,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.isAvailable), isTrue);
      });

      test('should filter by verification status', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', isVerified: true),
          _createTestAccommodation(id: '2', isVerified: false),
          _createTestAccommodation(id: '3', isVerified: true),
        ];

        // Act
        final result = searchEngine.filterByVerification(
          accommodations: accommodations,
          isVerified: true,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.isVerified), isTrue);
      });
    });

    group('Sorting Functionality', () {
      test('should sort by price in ascending order', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', price: 80000),
          _createTestAccommodation(id: '2', price: 30000),
          _createTestAccommodation(id: '3', price: 50000),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.price,
          sortOrder: SortOrder.ascending,
        );

        // Assert
        expect(result[0].pricing.monthly, equals(30000));
        expect(result[1].pricing.monthly, equals(50000));
        expect(result[2].pricing.monthly, equals(80000));
      });

      test('should sort by price in descending order', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', price: 80000),
          _createTestAccommodation(id: '2', price: 30000),
          _createTestAccommodation(id: '3', price: 50000),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.price,
          sortOrder: SortOrder.descending,
        );

        // Assert
        expect(result[0].pricing.monthly, equals(80000));
        expect(result[1].pricing.monthly, equals(50000));
        expect(result[2].pricing.monthly, equals(30000));
      });

      test('should sort by rating in descending order', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', rating: 3.0),
          _createTestAccommodation(id: '2', rating: 4.5),
          _createTestAccommodation(id: '3', rating: 2.5),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.rating,
          sortOrder: SortOrder.descending,
        );

        // Assert
        expect(result[0].rating, equals(4.5));
        expect(result[1].rating, equals(3.0));
        expect(result[2].rating, equals(2.5));
      });

      test('should sort by distance from campus', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', distanceFromCampus: 5.0),
          _createTestAccommodation(id: '2', distanceFromCampus: 1.0),
          _createTestAccommodation(id: '3', distanceFromCampus: 3.0),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.distance,
          sortOrder: SortOrder.ascending,
        );

        // Assert
        expect(result[0].location.distanceFromCampus, equals(1.0));
        expect(result[1].location.distanceFromCampus, equals(3.0));
        expect(result[2].location.distanceFromCampus, equals(5.0));
      });
    });

    group('Advanced Combined Filtering', () {
      test('should apply multiple filters simultaneously', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(
            id: '1',
            price: 45000,
            rating: 4.5,
            amenities: ['WiFi', 'Security', 'Parking'],
            type: AccommodationType.apartment,
            isAvailable: true,
            isVerified: true,
            distanceFromCampus: 1.0,
          ),
          _createTestAccommodation(
            id: '2',
            price: 60000, // Too expensive
            rating: 4.0,
            amenities: ['WiFi', 'Security'],
            type: AccommodationType.apartment,
            isAvailable: true,
            isVerified: true,
            distanceFromCampus: 2.0,
          ),
          _createTestAccommodation(
            id: '3',
            price: 30000,
            rating: 3.0, // Too low rating
            amenities: ['WiFi'],
            type: AccommodationType.sharedRoom,
            isAvailable: false, // Not available
            isVerified: false,
            distanceFromCampus: 3.0,
          ),
        ];

        // Act
        final result = searchEngine.applyAdvancedFilters(
          accommodations: accommodations,
          minPrice: 40000,
          maxPrice: 50000,
          minRating: 4.0,
          requiredAmenities: ['WiFi', 'Security'],
          type: AccommodationType.apartment,
          isAvailable: true,
          isVerified: true,
          maxDistanceFromCampus: 2.0,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.id, equals('1'));
      });

      test('should return empty list when no accommodations match all filters', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(
            id: '1',
            price: 30000, // Too cheap
            rating: 4.5,
            amenities: ['WiFi', 'Security'],
            type: AccommodationType.apartment,
            isAvailable: true,
            isVerified: true,
          ),
        ];

        // Act
        final result = searchEngine.applyAdvancedFilters(
          accommodations: accommodations,
          minPrice: 40000,
          maxPrice: 50000,
        );

        // Assert
        expect(result, isEmpty);
      });
    });
  });
}

// Helper function to create test accommodations
AccommodationModel _createTestAccommodation({
  required String id,
  String title = 'Test Accommodation',
  String description = 'Test description',
  AccommodationType type = AccommodationType.apartment,
  double latitude = 6.5244,
  double longitude = 3.3792,
  String address = 'Test Address',
  String? city = 'Lagos',
  String? state = 'Lagos',
  String? country = 'Nigeria',
  String? nearestCampus = 'University of Lagos',
  double distanceFromCampus = 1.0,
  double price = 50000,
  String currency = 'NGN',
  List<String> amenities = const ['WiFi', 'Security'],
  List<String> imageUrls = const [],
  String contactName = 'Test Owner',
  String contactPhone = '+2348012345678',
  String? contactEmail,
  double rating = 4.0,
  int reviewCount = 10,
  bool isVerified = true,
  bool isAvailable = true,
  String? ownerId = 'owner123',
}) {
  return AccommodationModel(
    id: id,
    title: title,
    description: description,
    type: type,
    location: LocationInfo(
      latitude: latitude,
      longitude: longitude,
      address: address,
      city: city,
      state: state,
      country: country,
      nearestCampus: nearestCampus,
      distanceFromCampus: distanceFromCampus,
    ),
    pricing: PricingInfo(
      monthly: price,
      currency: currency,
    ),
    amenities: amenities,
    imageUrls: imageUrls,
    contact: ContactInfo(
      name: contactName,
      phone: contactPhone,
      email: contactEmail,
    ),
    rating: rating,
    reviewCount: reviewCount,
    isVerified: isVerified,
    isAvailable: isAvailable,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    ownerId: ownerId,
    metadata: {},
  );
}