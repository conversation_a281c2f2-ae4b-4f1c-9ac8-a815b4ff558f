import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

import 'package:campus_find/core/models/accommodation_model.dart';
import 'package:campus_find/core/models/search_criteria.dart';
import 'package:campus_find/features/hostels/data/accommodation_repository.dart';
import 'package:campus_find/features/hostels/data/search_engine.dart';

// Mock class for testing
class MockAccommodationRepository extends Mock
    implements AccommodationRepository {}

void main() {
  group('SearchEngine Enhanced Features', () {
    late SearchEngine searchEngine;
    late MockAccommodationRepository mockRepository;

    setUp(() {
      mockRepository = MockAccommodationRepository();
      searchEngine = SearchEngine(repository: mockRepository);
    });

    group('Advanced Filtering', () {
      test('should filter by rating range correctly', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', rating: 4.5),
          _createTestAccommodation(id: '2', rating: 3.0),
          _createTestAccommodation(id: '3', rating: 2.5),
          _createTestAccommodation(id: '4', rating: 4.8),
        ];

        // Act
        final result = searchEngine.filterByRating(
          accommodations: accommodations,
          minRating: 3.5,
          maxRating: 4.6,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.rating, equals(4.5));
      });

      test('should filter by availability status', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', isAvailable: true),
          _createTestAccommodation(id: '2', isAvailable: false),
          _createTestAccommodation(id: '3', isAvailable: true),
        ];

        // Act
        final result = searchEngine.filterByAvailability(
          accommodations: accommodations,
          isAvailable: true,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.isAvailable), isTrue);
      });

      test('should filter by verification status', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', isVerified: true),
          _createTestAccommodation(id: '2', isVerified: false),
          _createTestAccommodation(id: '3', isVerified: true),
        ];

        // Act
        final result = searchEngine.filterByVerification(
          accommodations: accommodations,
          isVerified: true,
        );

        // Assert
        expect(result.length, equals(2));
        expect(result.every((acc) => acc.isVerified), isTrue);
      });

      test('should apply multiple advanced filters', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(
            id: '1',
            price: 45000,
            rating: 4.5,
            amenities: ['WiFi', 'Security', 'Parking'],
            type: AccommodationType.apartment,
            isAvailable: true,
            isVerified: true,
            distanceFromCampus: 1.0,
          ),
          _createTestAccommodation(
            id: '2',
            price: 60000, // Too expensive
            rating: 4.0,
            amenities: ['WiFi', 'Security'],
            type: AccommodationType.apartment,
            isAvailable: true,
            isVerified: true,
            distanceFromCampus: 2.0,
          ),
          _createTestAccommodation(
            id: '3',
            price: 30000,
            rating: 3.0, // Too low rating
            amenities: ['WiFi'],
            type: AccommodationType.sharedRoom,
            isAvailable: false, // Not available
            isVerified: false,
            distanceFromCampus: 3.0,
          ),
        ];

        // Act
        final result = searchEngine.applyAdvancedFilters(
          accommodations: accommodations,
          minPrice: 40000,
          maxPrice: 50000,
          minRating: 4.0,
          requiredAmenities: ['WiFi', 'Security'],
          type: AccommodationType.apartment,
          isAvailable: true,
          isVerified: true,
          maxDistanceFromCampus: 2.0,
        );

        // Assert
        expect(result.length, equals(1));
        expect(result.first.id, equals('1'));
      });
    });

    group('Distance Calculations', () {
      test('should calculate distance between two points correctly', () {
        // Arrange - University of Lagos coordinates
        const lat1 = 6.5244;
        const lon1 = 3.3792;
        const lat2 = 6.5200; // Nearby location
        const lon2 = 3.3800;

        // Act
        final distance = searchEngine.calculateDistance(lat1, lon1, lat2, lon2);

        // Assert
        expect(distance, greaterThan(0));
        expect(distance, lessThan(1)); // Should be less than 1km
      });

      test('should return zero for identical coordinates', () {
        // Arrange
        const lat = 6.5244;
        const lon = 3.3792;

        // Act
        final distance = searchEngine.calculateDistance(lat, lon, lat, lon);

        // Assert
        expect(distance, equals(0));
      });
    });

    group('Sorting Functionality', () {
      test('should sort by price in ascending order', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', price: 80000),
          _createTestAccommodation(id: '2', price: 30000),
          _createTestAccommodation(id: '3', price: 50000),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.price,
          sortOrder: SortOrder.ascending,
        );

        // Assert
        expect(result[0].pricing.monthly, equals(30000));
        expect(result[1].pricing.monthly, equals(50000));
        expect(result[2].pricing.monthly, equals(80000));
      });

      test('should sort by rating in descending order', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', rating: 3.0),
          _createTestAccommodation(id: '2', rating: 4.5),
          _createTestAccommodation(id: '3', rating: 2.5),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.rating,
          sortOrder: SortOrder.descending,
        );

        // Assert
        expect(result[0].rating, equals(4.5));
        expect(result[1].rating, equals(3.0));
        expect(result[2].rating, equals(2.5));
      });

      test('should sort by distance from campus', () {
        // Arrange
        final accommodations = [
          _createTestAccommodation(id: '1', distanceFromCampus: 5.0),
          _createTestAccommodation(id: '2', distanceFromCampus: 1.0),
          _createTestAccommodation(id: '3', distanceFromCampus: 3.0),
        ];

        // Act
        final result = searchEngine.sortAccommodations(
          accommodations: accommodations,
          sortBy: SortOption.distance,
          sortOrder: SortOrder.ascending,
        );

        // Assert
        expect(result[0].location.distanceFromCampus, equals(1.0));
        expect(result[1].location.distanceFromCampus, equals(3.0));
        expect(result[2].location.distanceFromCampus, equals(5.0));
      });
    });

    group('Search Suggestions', () {
      test('should return empty list for queries shorter than 2 characters',
          () async {
        // Act
        final result = await searchEngine.getSearchSuggestions('a');

        // Assert
        expect(result, isEmpty);
      });

      test('should handle search suggestions with mock data', () async {
        // Arrange
        const query = 'apart';
        final mockAccommodations = [
          _createTestAccommodation(
            id: '1',
            title: 'Student Apartment Complex',
            address: 'University Road, Lagos',
            city: 'Lagos',
            amenities: ['WiFi', 'Parking'],
          ),
        ];

        when(mockRepository.getAll(limit: 100))
            .thenAnswer((_) async => mockAccommodations);

        // Act
        final result = await searchEngine.getSearchSuggestions(query);

        // Assert
        expect(result, isNotEmpty);
        expect(result, contains('Student Apartment Complex'));
        verify(mockRepository.getAll(limit: 100)).called(1);
      });
    });

    group('Location-based Search', () {
      test('should handle location-based search with filtering', () async {
        // Arrange
        const latitude = 6.5244;
        const longitude = 3.3792;
        const radiusInKm = 2.0;

        final nearbyAccommodations = [
          _createTestAccommodation(
            id: '1',
            title: 'Nearby Apartment',
            latitude: 6.5250, // Very close
            longitude: 3.3800,
            distanceFromCampus: 0.5,
          ),
          _createTestAccommodation(
            id: '2',
            title: 'Far Apartment',
            latitude: 6.6000, // Far away
            longitude: 3.4000,
            distanceFromCampus: 10.0,
          ),
        ];

        when(mockRepository.getNearLocation(
          latitude: latitude,
          longitude: longitude,
          radiusInKm: radiusInKm,
          limit: 60, // 20 * 3
        )).thenAnswer((_) async => nearbyAccommodations);

        // Act
        final result = await searchEngine.searchNearLocation(
          latitude: latitude,
          longitude: longitude,
          radiusInKm: radiusInKm,
          limit: 20,
        );

        // Assert
        expect(result.length, greaterThan(0));
        // All results should be within the radius (calculated distance)
        for (final accommodation in result) {
          final distance = searchEngine.calculateDistance(
            latitude,
            longitude,
            accommodation.location.latitude,
            accommodation.location.longitude,
          );
          expect(distance, lessThanOrEqualTo(radiusInKm));
        }

        verify(mockRepository.getNearLocation(
          latitude: latitude,
          longitude: longitude,
          radiusInKm: radiusInKm,
          limit: 60,
        )).called(1);
      });
    });
  });
}

// Helper function to create test accommodations
AccommodationModel _createTestAccommodation({
  required String id,
  String title = 'Test Accommodation',
  String description = 'Test description',
  AccommodationType type = AccommodationType.apartment,
  double latitude = 6.5244,
  double longitude = 3.3792,
  String address = 'Test Address',
  String? city = 'Lagos',
  String? state = 'Lagos',
  String? country = 'Nigeria',
  String? nearestCampus = 'University of Lagos',
  double distanceFromCampus = 1.0,
  double price = 50000,
  String currency = 'NGN',
  List<String> amenities = const ['WiFi', 'Security'],
  List<String> imageUrls = const [],
  String contactName = 'Test Owner',
  String contactPhone = '+2348012345678',
  String? contactEmail,
  double rating = 4.0,
  int reviewCount = 10,
  bool isVerified = true,
  bool isAvailable = true,
  String? ownerId = 'owner123',
  Map<String, dynamic>? metadata,
}) {
  return AccommodationModel(
    id: id,
    title: title,
    description: description,
    type: type,
    location: LocationInfo(
      latitude: latitude,
      longitude: longitude,
      address: address,
      city: city,
      state: state,
      country: country,
      nearestCampus: nearestCampus,
      distanceFromCampus: distanceFromCampus,
    ),
    pricing: PricingInfo(
      monthly: price,
      currency: currency,
    ),
    amenities: amenities,
    imageUrls: imageUrls,
    contact: ContactInfo(
      name: contactName,
      phone: contactPhone,
      email: contactEmail,
    ),
    rating: rating,
    reviewCount: reviewCount,
    isVerified: isVerified,
    isAvailable: isAvailable,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    ownerId: ownerId,
    metadata: metadata ?? {},
  );
}
