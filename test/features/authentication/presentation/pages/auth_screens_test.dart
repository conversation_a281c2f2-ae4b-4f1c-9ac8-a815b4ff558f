import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:campus_find/features/authentication/presentation/pages/login_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/register_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/password_reset_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/email_verification_screen.dart';

void main() {
  Widget createTestWidget(Widget child) {
    return ProviderScope(
      child: MaterialApp(
        home: child,
        routes: {
          '/register': (context) => const RegisterScreen(),
          '/password-reset': (context) => const PasswordResetScreen(),
          '/accommodation': (context) => const Scaffold(body: Text('Accommodation')),
          '/login': (context) => const LoginScreen(),
        },
      ),
    );
  }

  group('LoginScreen UI Tests', () {
    testWidgets('should display all required UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const LoginScreen()));

      // Check for main title
      expect(find.text('Campus Find'), findsOneWidget);
      expect(find.text('Find your perfect accommodation'), findsOneWidget);

      // Check for social media buttons
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Continue with Facebook'), findsOneWidget);

      // Check for form fields
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);

      // Check for buttons
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);
      expect(find.text('Sign Up'), findsOneWidget);
    });

    testWidgets('should validate email field with invalid input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const LoginScreen()));

      // Find the email field and enter invalid email
      final emailField = find.byType(TextFormField).first;
      await tester.enterText(emailField, 'invalid-email');
      
      // Tap sign in button to trigger validation
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should validate password field with short input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const LoginScreen()));

      // Find the password field and enter short password
      final passwordField = find.byType(TextFormField).last;
      await tester.enterText(passwordField, '123');
      
      // Tap sign in button to trigger validation
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const LoginScreen()));

      // Find the password visibility toggle button
      final visibilityButton = find.byIcon(Icons.visibility);
      expect(visibilityButton, findsOneWidget);

      // Tap to toggle visibility
      await tester.tap(visibilityButton);
      await tester.pump();

      // Should now show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should navigate to register screen', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const LoginScreen()));

      // Tap on sign up link
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      // Should navigate to register screen
      expect(find.byType(RegisterScreen), findsOneWidget);
    });

    testWidgets('should navigate to password reset screen', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const LoginScreen()));

      // Tap on forgot password link
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();

      // Should navigate to password reset screen
      expect(find.byType(PasswordResetScreen), findsOneWidget);
    });
  });

  group('RegisterScreen UI Tests', () {
    testWidgets('should display all required UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      // Check for main title
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Join Campus Find to discover your perfect accommodation'), findsOneWidget);

      // Check for social media buttons
      expect(find.text('Sign up with Google'), findsOneWidget);
      expect(find.text('Sign up with Facebook'), findsOneWidget);

      // Check for form fields
      expect(find.byType(TextFormField), findsNWidgets(6));
      expect(find.text('Full Name *'), findsOneWidget);
      expect(find.text('Email *'), findsOneWidget);
      expect(find.text('Phone Number (Optional)'), findsOneWidget);
      expect(find.text('Campus/University (Optional)'), findsOneWidget);
      expect(find.text('Password *'), findsOneWidget);
      expect(find.text('Confirm Password *'), findsOneWidget);

      // Check for buttons
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
    });

    testWidgets('should validate required fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      // Tap create account button without filling fields
      await tester.tap(find.text('Create Account'));
      await tester.pump();

      // Should show validation errors for required fields
      expect(find.text('Full name is required'), findsOneWidget);
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });

    testWidgets('should validate password strength', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      // Find password field and enter weak password
      final passwordFields = find.byType(TextFormField);
      await tester.enterText(passwordFields.at(4), 'weak');
      
      // Tap create account button to trigger validation
      await tester.tap(find.text('Create Account'));
      await tester.pump();

      // Should show password strength validation error
      expect(find.text('Password must contain at least one letter and one number'), findsOneWidget);
    });

    testWidgets('should validate password confirmation', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      // Find password fields
      final passwordFields = find.byType(TextFormField);
      await tester.enterText(passwordFields.at(4), 'password123');
      await tester.enterText(passwordFields.at(5), 'different123');
      
      // Tap create account button to trigger validation
      await tester.tap(find.text('Create Account'));
      await tester.pump();

      // Should show password mismatch error
      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    testWidgets('should toggle password visibility for both password fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const RegisterScreen()));

      // Find password visibility toggle buttons
      final visibilityButtons = find.byIcon(Icons.visibility);
      expect(visibilityButtons, findsNWidgets(2));

      // Tap first password field visibility toggle
      await tester.tap(visibilityButtons.first);
      await tester.pump();

      // Should show one visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });
  });

  group('PasswordResetScreen UI Tests', () {
    testWidgets('should display reset form initially', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const PasswordResetScreen()));

      // Check for main elements
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Enter your email address and we\'ll send you a link to reset your password.'), findsOneWidget);
      expect(find.byIcon(Icons.lock_reset), findsOneWidget);

      // Check for form field and button
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.text('Back to Login'), findsOneWidget);
    });

    testWidgets('should validate email field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const PasswordResetScreen()));

      // Enter invalid email
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      
      // Tap send reset link button
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should accept valid email format', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const PasswordResetScreen()));

      // Enter valid email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      
      // Tap send reset link button (this will fail due to no auth service, but validation should pass)
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Should not show email validation error
      expect(find.text('Please enter a valid email'), findsNothing);
    });
  });

  group('EmailVerificationScreen UI Tests', () {
    testWidgets('should display verification UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const EmailVerificationScreen()));

      // Check for main elements
      expect(find.text('Verify Your Email'), findsOneWidget);
      expect(find.text('We\'ve sent a verification email to:'), findsOneWidget);
      expect(find.byIcon(Icons.mark_email_unread), findsOneWidget);

      // Check for buttons
      expect(find.text('I\'ve Verified My Email'), findsOneWidget);
      expect(find.text('Resend Verification Email'), findsOneWidget);
      expect(find.text('Sign Out'), findsOneWidget);
    });

    testWidgets('should display info message about spam folder', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(const EmailVerificationScreen()));

      // Check for info message
      expect(find.text('Don\'t forget to check your spam folder if you don\'t see the email.'), findsOneWidget);
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });
  });
}