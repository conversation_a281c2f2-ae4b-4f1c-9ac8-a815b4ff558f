import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:campus_find/features/authentication/presentation/pages/login_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/register_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/password_reset_screen.dart';

void main() {
  group('Authentication UI Basic Tests', () {
    testWidgets('LoginScreen should display all required UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginScreen(),
          ),
        ),
      );

      // Check for main UI elements
      expect(find.text('Campus Find'), findsOneWidget);
      expect(find.text('Find your perfect accommodation'), findsOneWidget);
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Continue with Facebook'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and Password
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);
      expect(find.text('Sign Up'), findsOneWidget);
    });

    testWidgets('RegisterScreen should display all required UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: RegisterScreen(),
          ),
        ),
      );

      // Check for main UI elements
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Join Campus Find to discover your perfect accommodation'), findsOneWidget);
      expect(find.text('Sign up with Google'), findsOneWidget);
      expect(find.text('Sign up with Facebook'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(6)); // All form fields
      expect(find.text('Create Account'), findsNWidgets(2)); // Title and button
      expect(find.text('Sign In'), findsOneWidget);
    });

    testWidgets('PasswordResetScreen should display reset form', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PasswordResetScreen(),
          ),
        ),
      );

      // Check for main UI elements
      expect(find.text('Reset Password'), findsNWidgets(2)); // Title and button
      expect(find.text('Enter your email address and we\'ll send you a link to reset your password.'), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget); // Email field
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.text('Back to Login'), findsOneWidget);
    });

    testWidgets('LoginScreen should validate email field', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginScreen(),
          ),
        ),
      );

      // Find email field and enter invalid email
      final emailField = find.byType(TextFormField).first;
      await tester.enterText(emailField, 'invalid-email');
      
      // Tap sign in button to trigger validation
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('RegisterScreen should validate password confirmation', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: RegisterScreen(),
          ),
        ),
      );

      // Find password fields
      final passwordFields = find.byType(TextFormField);
      final passwordField = passwordFields.at(4); // Password field (5th field)
      final confirmPasswordField = passwordFields.at(5); // Confirm password field (6th field)
      
      // Enter different passwords
      await tester.enterText(passwordField, 'password123');
      await tester.enterText(confirmPasswordField, 'different123');
      
      // Tap create account button to trigger validation
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Should show validation error
      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    testWidgets('Password visibility toggle should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginScreen(),
          ),
        ),
      );

      // Find password field and visibility toggle
      final passwordField = find.byType(TextFormField).last;
      final visibilityToggle = find.byIcon(Icons.visibility);
      
      // Initially password should be obscured
      expect(visibilityToggle, findsOneWidget);
      
      // Tap visibility toggle
      await tester.tap(visibilityToggle);
      await tester.pump();
      
      // Now should show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });
  });
}