import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:campus_find/features/authentication/presentation/pages/login_screen.dart';

void main() {
  Widget createTestWidget() {
    return ProviderScope(
      child: MaterialApp(
        home: const LoginScreen(),
        routes: {
          '/register': (context) => const Scaffold(body: Text('Register')),
          '/password-reset': (context) => const Scaffold(body: Text('Password Reset')),
          '/accommodation': (context) => const Scaffold(body: Text('Accommodation')),
        },
      ),
    );
  }

  group('LoginScreen UI Tests', () {
    testWidgets('should display all required UI elements', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check for main title
      expect(find.text('Campus Find'), findsOneWidget);
      expect(find.text('Find your perfect accommodation'), findsOneWidget);

      // Check for social media buttons
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Continue with Facebook'), findsOneWidget);

      // Check for form fields
      expect(find.byType(TextFormField), findsNWidgets(2));
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);

      // Check for buttons
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);
      expect(find.text('Sign Up'), findsOneWidget);
    });

    testWidgets('should validate email field with invalid input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the email field and enter invalid email
      final emailField = find.byType(TextFormField).first;
      await tester.enterText(emailField, 'invalid-email');
      
      // Tap sign in button to trigger validation
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should validate password field with short input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the password field and enter short password
      final passwordField = find.byType(TextFormField).last;
      await tester.enterText(passwordField, '123');
      
      // Tap sign in button to trigger validation
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show validation error
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the password visibility toggle button
      final visibilityButton = find.byIcon(Icons.visibility);
      expect(visibilityButton, findsOneWidget);

      // Tap to toggle visibility
      await tester.tap(visibilityButton);
      await tester.pump();

      // Should now show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should show loading state when form is valid', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter valid credentials
      final emailField = find.byType(TextFormField).first;
      final passwordField = find.byType(TextFormField).last;
      
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password123');
      
      // Tap sign in button
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show loading indicator (briefly before error occurs)
      // Note: This will fail due to no auth service, but validation should pass
      expect(find.text('Please enter a valid email'), findsNothing);
      expect(find.text('Password must be at least 6 characters'), findsNothing);
    });

    testWidgets('should accept valid email and password', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Enter valid credentials
      final emailField = find.byType(TextFormField).first;
      final passwordField = find.byType(TextFormField).last;
      
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'password123');
      
      // Tap sign in button
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should not show validation errors
      expect(find.text('Please enter a valid email'), findsNothing);
      expect(find.text('Password must be at least 6 characters'), findsNothing);
    });
  });
}