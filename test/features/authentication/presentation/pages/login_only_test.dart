import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:campus_find/features/authentication/presentation/pages/login_screen.dart';

void main() {
  Widget createTestWidget() {
    return ProviderScope(
      child: MaterialApp(
        home: const LoginScreen(),
      ),
    );
  }

  group('LoginScreen Tests', () {
    testWidgets('should render without errors', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Should find the main title
      expect(find.text('Campus Find'), findsOneWidget);
      expect(find.text('Find your perfect accommodation'), findsOneWidget);
      
      // Should find form fields
      expect(find.byType(TextFormField), findsNWidgets(2));
      
      // Should find main buttons
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Continue with Facebook'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
    });

    testWidgets('should have email and password fields', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Should find email and password labels
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
    });

    testWidgets('should have social media login options', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Should find social media buttons
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Continue with Facebook'), findsOneWidget);
    });

    testWidgets('should have forgot password link', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Should find forgot password link
      expect(find.text('Forgot Password?'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Find the password visibility toggle button
      final visibilityButton = find.byIcon(Icons.visibility);
      expect(visibilityButton, findsOneWidget);
      
      // Tap to toggle visibility
      await tester.tap(visibilityButton);
      await tester.pump();
      
      // Should now show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });
  });
}