import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:campus_find/features/authentication/presentation/pages/login_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/register_screen.dart';
import 'package:campus_find/features/authentication/presentation/pages/password_reset_screen.dart';

void main() {
  group('Authentication UI Validation Tests', () {
    testWidgets('LoginScreen should display core elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginScreen(),
          ),
        ),
      );

      // Check for main UI elements
      expect(find.text('Campus Find'), findsOneWidget);
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Continue with Facebook'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and Password
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);
    });

    testWidgets('RegisterScreen should display core elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: RegisterScreen(),
          ),
        ),
      );

      // Check for main UI elements
      expect(find.text('Create Account'), findsAtLeastNWidgets(1));
      expect(find.text('Sign up with Google'), findsOneWidget);
      expect(find.text('Sign up with Facebook'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(6)); // All form fields
    });

    testWidgets('PasswordResetScreen should display core elements', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: PasswordResetScreen(),
          ),
        ),
      );

      // Check for main UI elements
      expect(find.text('Reset Password'), findsAtLeastNWidgets(1));
      expect(find.byType(TextFormField), findsOneWidget); // Email field
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.text('Back to Login'), findsOneWidget);
    });

    testWidgets('LoginScreen should have proper form validation', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginScreen(),
          ),
        ),
      );

      // Test empty form validation
      final signInButton = find.text('Sign In');
      await tester.tap(signInButton);
      await tester.pump();

      // Should show validation errors for empty fields
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });

    testWidgets('RegisterScreen should validate required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: RegisterScreen(),
          ),
        ),
      );

      // Test empty form validation
      final createAccountButton = find.text('Create Account').last;
      await tester.tap(createAccountButton);
      await tester.pump();

      // Should show validation errors for required fields
      expect(find.text('Full name is required'), findsOneWidget);
      expect(find.text('Email is required'), findsOneWidget);
      expect(find.text('Password is required'), findsOneWidget);
    });

    testWidgets('Password visibility toggle should work in LoginScreen', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: LoginScreen(),
          ),
        ),
      );

      // Find password field and visibility toggle
      final visibilityToggle = find.byIcon(Icons.visibility);
      
      // Initially password should be obscured
      expect(visibilityToggle, findsOneWidget);
      
      // Tap visibility toggle
      await tester.tap(visibilityToggle);
      await tester.pump();
      
      // Now should show visibility_off icon
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('RegisterScreen should validate email format', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: RegisterScreen(),
          ),
        ),
      );

      // Find form fields
      final formFields = find.byType(TextFormField);
      final nameField = formFields.at(0);
      final emailField = formFields.at(1);
      
      // Enter valid name but invalid email
      await tester.enterText(nameField, 'John Doe');
      await tester.enterText(emailField, 'invalid-email');
      
      // Tap create account button to trigger validation
      final createAccountButton = find.text('Create Account').last;
      await tester.tap(createAccountButton);
      await tester.pump();

      // Should show email validation error
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('RegisterScreen should validate password strength', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: RegisterScreen(),
          ),
        ),
      );

      // Find form fields
      final formFields = find.byType(TextFormField);
      final nameField = formFields.at(0);
      final emailField = formFields.at(1);
      final passwordField = formFields.at(4);
      
      // Enter valid name and email but weak password
      await tester.enterText(nameField, 'John Doe');
      await tester.enterText(emailField, '<EMAIL>');
      await tester.enterText(passwordField, 'weak');
      
      // Tap create account button to trigger validation
      final createAccountButton = find.text('Create Account').last;
      await tester.tap(createAccountButton);
      await tester.pump();

      // Should show password validation errors
      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });
  });
}