import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/features/profile/presentation/widgets/profile_form_fields.dart';
import '../../../lib/features/profile/presentation/widgets/profile_image_picker.dart';

void main() {
  group('Profile Widget Tests', () {
    testWidgets('ProfileFormFields should display all form fields', (WidgetTester tester) async {
      // Arrange
      final fullNameController = TextEditingController();
      final phoneNumberController = TextEditingController();
      final campusController = TextEditingController();
      bool receiveNotifications = true;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProfileFormFields(
              fullNameController: fullNameController,
              phoneNumberController: phoneNumberController,
              campusController: campusController,
              receiveNotifications: receiveNotifications,
              onNotificationChanged: (value) {
                receiveNotifications = value;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Full Name'), findsOneWidget);
      expect(find.text('Phone Number'), findsOneWidget);
      expect(find.text('Campus/University'), findsOneWidget);
      expect(find.text('Receive Notifications'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(3));
      expect(find.byType(Switch), findsOneWidget);
    });

    testWidgets('ProfileFormFields should validate full name field', (WidgetTester tester) async {
      // Arrange
      final fullNameController = TextEditingController();
      final phoneNumberController = TextEditingController();
      final campusController = TextEditingController();
      bool receiveNotifications = true;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: ProfileFormFields(
                fullNameController: fullNameController,
                phoneNumberController: phoneNumberController,
                campusController: campusController,
                receiveNotifications: receiveNotifications,
                onNotificationChanged: (value) {
                  receiveNotifications = value;
                },
              ),
            ),
          ),
        ),
      );

      // Act - Enter empty text and trigger validation
      final fullNameField = find.byType(TextFormField).first;
      await tester.enterText(fullNameField, '');
      await tester.pump();

      // Trigger form validation by finding the TextFormField widget and calling its validator
      final textFormField = tester.widget<TextFormField>(fullNameField);
      final validationResult = textFormField.validator!('');

      // Assert
      expect(validationResult, equals('Full name is required'));
    });

    testWidgets('ProfileFormFields should validate phone number field', (WidgetTester tester) async {
      // Arrange
      final fullNameController = TextEditingController();
      final phoneNumberController = TextEditingController();
      final campusController = TextEditingController();
      bool receiveNotifications = true;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: ProfileFormFields(
                fullNameController: fullNameController,
                phoneNumberController: phoneNumberController,
                campusController: campusController,
                receiveNotifications: receiveNotifications,
                onNotificationChanged: (value) {
                  receiveNotifications = value;
                },
              ),
            ),
          ),
        ),
      );

      // Act - Enter invalid phone number
      final phoneField = find.byType(TextFormField).at(1); // Second field is phone
      await tester.enterText(phoneField, '123');
      await tester.pump();

      // Trigger form validation
      final textFormField = tester.widget<TextFormField>(phoneField);
      final validationResult = textFormField.validator!('123');

      // Assert
      expect(validationResult, equals('Please enter a valid phone number'));
    });

    testWidgets('ProfileImagePicker should display default profile icon', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProfileImagePicker(
              onImageSelected: (image) {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.person), findsOneWidget);
      expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    });

    testWidgets('ProfileImagePicker should show loading state', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProfileImagePicker(
              onImageSelected: (image) {},
              isLoading: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('ProfileImagePicker should display network image when provided', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ProfileImagePicker(
              imageUrl: 'https://example.com/image.jpg',
              onImageSelected: (image) {},
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(Image), findsOneWidget);
    });
  });
}