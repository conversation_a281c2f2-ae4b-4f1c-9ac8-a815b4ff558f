// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in campus_find/test/features/profile/profile_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:io' as _i10;
import 'dart:typed_data' as _i9;

import 'package:cloud_firestore/cloud_firestore.dart' as _i4;
import 'package:cloud_firestore_platform_interface/cloud_firestore_platform_interface.dart'
    as _i3;
import 'package:firebase_core/firebase_core.dart' as _i2;
import 'package:firebase_storage/firebase_storage.dart' as _i6;
import 'package:firebase_storage_platform_interface/firebase_storage_platform_interface.dart'
    as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseApp_0 extends _i1.SmartFake implements _i2.FirebaseApp {
  _FakeFirebaseApp_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSettings_1 extends _i1.SmartFake implements _i3.Settings {
  _FakeSettings_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCollectionReference_2<T extends Object?> extends _i1.SmartFake
    implements _i4.CollectionReference<T> {
  _FakeCollectionReference_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWriteBatch_3 extends _i1.SmartFake implements _i4.WriteBatch {
  _FakeWriteBatch_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLoadBundleTask_4 extends _i1.SmartFake
    implements _i4.LoadBundleTask {
  _FakeLoadBundleTask_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQuerySnapshot_5<T1 extends Object?> extends _i1.SmartFake
    implements _i4.QuerySnapshot<T1> {
  _FakeQuerySnapshot_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQuery_6<T extends Object?> extends _i1.SmartFake
    implements _i4.Query<T> {
  _FakeQuery_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDocumentReference_7<T extends Object?> extends _i1.SmartFake
    implements _i4.DocumentReference<T> {
  _FakeDocumentReference_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_8<T1> extends _i1.SmartFake implements _i5.Future<T1> {
  _FakeFuture_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFirebaseFirestore_9 extends _i1.SmartFake
    implements _i4.FirebaseFirestore {
  _FakeFirebaseFirestore_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAggregateQuery_10 extends _i1.SmartFake
    implements _i4.AggregateQuery {
  _FakeAggregateQuery_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDocumentSnapshot_11<T1 extends Object?> extends _i1.SmartFake
    implements _i4.DocumentSnapshot<T1> {
  _FakeDocumentSnapshot_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSnapshotMetadata_12 extends _i1.SmartFake
    implements _i4.SnapshotMetadata {
  _FakeSnapshotMetadata_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDuration_13 extends _i1.SmartFake implements Duration {
  _FakeDuration_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeReference_14 extends _i1.SmartFake implements _i6.Reference {
  _FakeReference_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFirebaseStorage_15 extends _i1.SmartFake
    implements _i6.FirebaseStorage {
  _FakeFirebaseStorage_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFullMetadata_16 extends _i1.SmartFake implements _i7.FullMetadata {
  _FakeFullMetadata_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeListResult_17 extends _i1.SmartFake implements _i6.ListResult {
  _FakeListResult_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUploadTask_18 extends _i1.SmartFake implements _i6.UploadTask {
  _FakeUploadTask_18(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDownloadTask_19 extends _i1.SmartFake implements _i6.DownloadTask {
  _FakeDownloadTask_19(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTaskSnapshot_20 extends _i1.SmartFake implements _i6.TaskSnapshot {
  _FakeTaskSnapshot_20(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [FirebaseFirestore].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseFirestore extends _i1.Mock implements _i4.FirebaseFirestore {
  MockFirebaseFirestore() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app => (super.noSuchMethod(
        Invocation.getter(#app),
        returnValue: _FakeFirebaseApp_0(
          this,
          Invocation.getter(#app),
        ),
      ) as _i2.FirebaseApp);

  @override
  String get databaseURL => (super.noSuchMethod(
        Invocation.getter(#databaseURL),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#databaseURL),
        ),
      ) as String);

  @override
  String get databaseId => (super.noSuchMethod(
        Invocation.getter(#databaseId),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#databaseId),
        ),
      ) as String);

  @override
  _i3.Settings get settings => (super.noSuchMethod(
        Invocation.getter(#settings),
        returnValue: _FakeSettings_1(
          this,
          Invocation.getter(#settings),
        ),
      ) as _i3.Settings);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
        Invocation.setter(
          #app,
          _app,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set databaseURL(String? _databaseURL) => super.noSuchMethod(
        Invocation.setter(
          #databaseURL,
          _databaseURL,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set databaseId(String? _databaseId) => super.noSuchMethod(
        Invocation.setter(
          #databaseId,
          _databaseId,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set settings(_i3.Settings? settings) => super.noSuchMethod(
        Invocation.setter(
          #settings,
          settings,
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<dynamic, dynamic> get pluginConstants => (super.noSuchMethod(
        Invocation.getter(#pluginConstants),
        returnValue: <dynamic, dynamic>{},
      ) as Map<dynamic, dynamic>);

  @override
  _i4.CollectionReference<Map<String, dynamic>> collection(
          String? collectionPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #collection,
          [collectionPath],
        ),
        returnValue: _FakeCollectionReference_2<Map<String, dynamic>>(
          this,
          Invocation.method(
            #collection,
            [collectionPath],
          ),
        ),
      ) as _i4.CollectionReference<Map<String, dynamic>>);

  @override
  _i4.WriteBatch batch() => (super.noSuchMethod(
        Invocation.method(
          #batch,
          [],
        ),
        returnValue: _FakeWriteBatch_3(
          this,
          Invocation.method(
            #batch,
            [],
          ),
        ),
      ) as _i4.WriteBatch);

  @override
  _i5.Future<void> clearPersistence() => (super.noSuchMethod(
        Invocation.method(
          #clearPersistence,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> enablePersistence(
          [_i3.PersistenceSettings? persistenceSettings]) =>
      (super.noSuchMethod(
        Invocation.method(
          #enablePersistence,
          [persistenceSettings],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i4.LoadBundleTask loadBundle(_i9.Uint8List? bundle) => (super.noSuchMethod(
        Invocation.method(
          #loadBundle,
          [bundle],
        ),
        returnValue: _FakeLoadBundleTask_4(
          this,
          Invocation.method(
            #loadBundle,
            [bundle],
          ),
        ),
      ) as _i4.LoadBundleTask);

  @override
  void useFirestoreEmulator(
    String? host,
    int? port, {
    bool? sslEnabled = false,
    bool? automaticHostMapping = true,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #useFirestoreEmulator,
          [
            host,
            port,
          ],
          {
            #sslEnabled: sslEnabled,
            #automaticHostMapping: automaticHostMapping,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<_i4.QuerySnapshot<T>> namedQueryWithConverterGet<T>(
    String? name, {
    _i3.GetOptions? options = const _i3.GetOptions(),
    required _i4.FromFirestore<T>? fromFirestore,
    required _i4.ToFirestore<T>? toFirestore,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #namedQueryWithConverterGet,
          [name],
          {
            #options: options,
            #fromFirestore: fromFirestore,
            #toFirestore: toFirestore,
          },
        ),
        returnValue:
            _i5.Future<_i4.QuerySnapshot<T>>.value(_FakeQuerySnapshot_5<T>(
          this,
          Invocation.method(
            #namedQueryWithConverterGet,
            [name],
            {
              #options: options,
              #fromFirestore: fromFirestore,
              #toFirestore: toFirestore,
            },
          ),
        )),
      ) as _i5.Future<_i4.QuerySnapshot<T>>);

  @override
  _i5.Future<_i4.QuerySnapshot<Map<String, dynamic>>> namedQueryGet(
    String? name, {
    _i3.GetOptions? options = const _i3.GetOptions(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #namedQueryGet,
          [name],
          {#options: options},
        ),
        returnValue: _i5.Future<_i4.QuerySnapshot<Map<String, dynamic>>>.value(
            _FakeQuerySnapshot_5<Map<String, dynamic>>(
          this,
          Invocation.method(
            #namedQueryGet,
            [name],
            {#options: options},
          ),
        )),
      ) as _i5.Future<_i4.QuerySnapshot<Map<String, dynamic>>>);

  @override
  _i4.Query<Map<String, dynamic>> collectionGroup(String? collectionPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #collectionGroup,
          [collectionPath],
        ),
        returnValue: _FakeQuery_6<Map<String, dynamic>>(
          this,
          Invocation.method(
            #collectionGroup,
            [collectionPath],
          ),
        ),
      ) as _i4.Query<Map<String, dynamic>>);

  @override
  _i5.Future<void> disableNetwork() => (super.noSuchMethod(
        Invocation.method(
          #disableNetwork,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i4.DocumentReference<Map<String, dynamic>> doc(String? documentPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #doc,
          [documentPath],
        ),
        returnValue: _FakeDocumentReference_7<Map<String, dynamic>>(
          this,
          Invocation.method(
            #doc,
            [documentPath],
          ),
        ),
      ) as _i4.DocumentReference<Map<String, dynamic>>);

  @override
  _i5.Future<void> enableNetwork() => (super.noSuchMethod(
        Invocation.method(
          #enableNetwork,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Stream<void> snapshotsInSync() => (super.noSuchMethod(
        Invocation.method(
          #snapshotsInSync,
          [],
        ),
        returnValue: _i5.Stream<void>.empty(),
      ) as _i5.Stream<void>);

  @override
  _i5.Future<T> runTransaction<T>(
    _i4.TransactionHandler<T>? transactionHandler, {
    Duration? timeout = const Duration(seconds: 30),
    int? maxAttempts = 5,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runTransaction,
          [transactionHandler],
          {
            #timeout: timeout,
            #maxAttempts: maxAttempts,
          },
        ),
        returnValue: _i8.ifNotNull(
              _i8.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runTransaction,
                  [transactionHandler],
                  {
                    #timeout: timeout,
                    #maxAttempts: maxAttempts,
                  },
                ),
              ),
              (T v) => _i5.Future<T>.value(v),
            ) ??
            _FakeFuture_8<T>(
              this,
              Invocation.method(
                #runTransaction,
                [transactionHandler],
                {
                  #timeout: timeout,
                  #maxAttempts: maxAttempts,
                },
              ),
            ),
      ) as _i5.Future<T>);

  @override
  _i5.Future<void> terminate() => (super.noSuchMethod(
        Invocation.method(
          #terminate,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> waitForPendingWrites() => (super.noSuchMethod(
        Invocation.method(
          #waitForPendingWrites,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setIndexConfiguration({
    required List<_i3.Index>? indexes,
    List<_i3.FieldOverrides>? fieldOverrides,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setIndexConfiguration,
          [],
          {
            #indexes: indexes,
            #fieldOverrides: fieldOverrides,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> setIndexConfigurationFromJSON(String? json) =>
      (super.noSuchMethod(
        Invocation.method(
          #setIndexConfigurationFromJSON,
          [json],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

/// A class which mocks [CollectionReference].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockCollectionReference<T extends Object?> extends _i1.Mock
    implements _i4.CollectionReference<T> {
  MockCollectionReference() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  String get path => (super.noSuchMethod(
        Invocation.getter(#path),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#path),
        ),
      ) as String);

  @override
  _i4.FirebaseFirestore get firestore => (super.noSuchMethod(
        Invocation.getter(#firestore),
        returnValue: _FakeFirebaseFirestore_9(
          this,
          Invocation.getter(#firestore),
        ),
      ) as _i4.FirebaseFirestore);

  @override
  Map<String, dynamic> get parameters => (super.noSuchMethod(
        Invocation.getter(#parameters),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i5.Future<_i4.DocumentReference<T>> add(T? data) => (super.noSuchMethod(
        Invocation.method(
          #add,
          [data],
        ),
        returnValue: _i5.Future<_i4.DocumentReference<T>>.value(
            _FakeDocumentReference_7<T>(
          this,
          Invocation.method(
            #add,
            [data],
          ),
        )),
      ) as _i5.Future<_i4.DocumentReference<T>>);

  @override
  _i4.DocumentReference<T> doc([String? path]) => (super.noSuchMethod(
        Invocation.method(
          #doc,
          [path],
        ),
        returnValue: _FakeDocumentReference_7<T>(
          this,
          Invocation.method(
            #doc,
            [path],
          ),
        ),
      ) as _i4.DocumentReference<T>);

  @override
  _i4.CollectionReference<R> withConverter<R extends Object?>({
    required _i4.FromFirestore<R>? fromFirestore,
    required _i4.ToFirestore<R>? toFirestore,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #withConverter,
          [],
          {
            #fromFirestore: fromFirestore,
            #toFirestore: toFirestore,
          },
        ),
        returnValue: _FakeCollectionReference_2<R>(
          this,
          Invocation.method(
            #withConverter,
            [],
            {
              #fromFirestore: fromFirestore,
              #toFirestore: toFirestore,
            },
          ),
        ),
      ) as _i4.CollectionReference<R>);

  @override
  _i4.Query<T> endAtDocument(_i4.DocumentSnapshot<Object?>? documentSnapshot) =>
      (super.noSuchMethod(
        Invocation.method(
          #endAtDocument,
          [documentSnapshot],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #endAtDocument,
            [documentSnapshot],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> endAt(Iterable<Object?>? values) => (super.noSuchMethod(
        Invocation.method(
          #endAt,
          [values],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #endAt,
            [values],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> endBeforeDocument(
          _i4.DocumentSnapshot<Object?>? documentSnapshot) =>
      (super.noSuchMethod(
        Invocation.method(
          #endBeforeDocument,
          [documentSnapshot],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #endBeforeDocument,
            [documentSnapshot],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> endBefore(Iterable<Object?>? values) => (super.noSuchMethod(
        Invocation.method(
          #endBefore,
          [values],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #endBefore,
            [values],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i5.Future<_i4.QuerySnapshot<T>> get([_i3.GetOptions? options]) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [options],
        ),
        returnValue:
            _i5.Future<_i4.QuerySnapshot<T>>.value(_FakeQuerySnapshot_5<T>(
          this,
          Invocation.method(
            #get,
            [options],
          ),
        )),
      ) as _i5.Future<_i4.QuerySnapshot<T>>);

  @override
  _i4.Query<T> limit(int? limit) => (super.noSuchMethod(
        Invocation.method(
          #limit,
          [limit],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #limit,
            [limit],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> limitToLast(int? limit) => (super.noSuchMethod(
        Invocation.method(
          #limitToLast,
          [limit],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #limitToLast,
            [limit],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i5.Stream<_i4.QuerySnapshot<T>> snapshots({
    bool? includeMetadataChanges = false,
    _i3.ListenSource? source = _i3.ListenSource.defaultSource,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #snapshots,
          [],
          {
            #includeMetadataChanges: includeMetadataChanges,
            #source: source,
          },
        ),
        returnValue: _i5.Stream<_i4.QuerySnapshot<T>>.empty(),
      ) as _i5.Stream<_i4.QuerySnapshot<T>>);

  @override
  _i4.Query<T> orderBy(
    Object? field, {
    bool? descending = false,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #orderBy,
          [field],
          {#descending: descending},
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #orderBy,
            [field],
            {#descending: descending},
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> startAfterDocument(
          _i4.DocumentSnapshot<Object?>? documentSnapshot) =>
      (super.noSuchMethod(
        Invocation.method(
          #startAfterDocument,
          [documentSnapshot],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #startAfterDocument,
            [documentSnapshot],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> startAfter(Iterable<Object?>? values) => (super.noSuchMethod(
        Invocation.method(
          #startAfter,
          [values],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #startAfter,
            [values],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> startAtDocument(
          _i4.DocumentSnapshot<Object?>? documentSnapshot) =>
      (super.noSuchMethod(
        Invocation.method(
          #startAtDocument,
          [documentSnapshot],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #startAtDocument,
            [documentSnapshot],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> startAt(Iterable<Object?>? values) => (super.noSuchMethod(
        Invocation.method(
          #startAt,
          [values],
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #startAt,
            [values],
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.Query<T> where(
    Object? field, {
    Object? isEqualTo,
    Object? isNotEqualTo,
    Object? isLessThan,
    Object? isLessThanOrEqualTo,
    Object? isGreaterThan,
    Object? isGreaterThanOrEqualTo,
    Object? arrayContains,
    Iterable<Object?>? arrayContainsAny,
    Iterable<Object?>? whereIn,
    Iterable<Object?>? whereNotIn,
    bool? isNull,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #where,
          [field],
          {
            #isEqualTo: isEqualTo,
            #isNotEqualTo: isNotEqualTo,
            #isLessThan: isLessThan,
            #isLessThanOrEqualTo: isLessThanOrEqualTo,
            #isGreaterThan: isGreaterThan,
            #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
            #arrayContains: arrayContains,
            #arrayContainsAny: arrayContainsAny,
            #whereIn: whereIn,
            #whereNotIn: whereNotIn,
            #isNull: isNull,
          },
        ),
        returnValue: _FakeQuery_6<T>(
          this,
          Invocation.method(
            #where,
            [field],
            {
              #isEqualTo: isEqualTo,
              #isNotEqualTo: isNotEqualTo,
              #isLessThan: isLessThan,
              #isLessThanOrEqualTo: isLessThanOrEqualTo,
              #isGreaterThan: isGreaterThan,
              #isGreaterThanOrEqualTo: isGreaterThanOrEqualTo,
              #arrayContains: arrayContains,
              #arrayContainsAny: arrayContainsAny,
              #whereIn: whereIn,
              #whereNotIn: whereNotIn,
              #isNull: isNull,
            },
          ),
        ),
      ) as _i4.Query<T>);

  @override
  _i4.AggregateQuery count() => (super.noSuchMethod(
        Invocation.method(
          #count,
          [],
        ),
        returnValue: _FakeAggregateQuery_10(
          this,
          Invocation.method(
            #count,
            [],
          ),
        ),
      ) as _i4.AggregateQuery);

  @override
  _i4.AggregateQuery aggregate(
    _i3.AggregateField? aggregateField1, [
    _i3.AggregateField? aggregateField2,
    _i3.AggregateField? aggregateField3,
    _i3.AggregateField? aggregateField4,
    _i3.AggregateField? aggregateField5,
    _i3.AggregateField? aggregateField6,
    _i3.AggregateField? aggregateField7,
    _i3.AggregateField? aggregateField8,
    _i3.AggregateField? aggregateField9,
    _i3.AggregateField? aggregateField10,
    _i3.AggregateField? aggregateField11,
    _i3.AggregateField? aggregateField12,
    _i3.AggregateField? aggregateField13,
    _i3.AggregateField? aggregateField14,
    _i3.AggregateField? aggregateField15,
    _i3.AggregateField? aggregateField16,
    _i3.AggregateField? aggregateField17,
    _i3.AggregateField? aggregateField18,
    _i3.AggregateField? aggregateField19,
    _i3.AggregateField? aggregateField20,
    _i3.AggregateField? aggregateField21,
    _i3.AggregateField? aggregateField22,
    _i3.AggregateField? aggregateField23,
    _i3.AggregateField? aggregateField24,
    _i3.AggregateField? aggregateField25,
    _i3.AggregateField? aggregateField26,
    _i3.AggregateField? aggregateField27,
    _i3.AggregateField? aggregateField28,
    _i3.AggregateField? aggregateField29,
    _i3.AggregateField? aggregateField30,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #aggregate,
          [
            aggregateField1,
            aggregateField2,
            aggregateField3,
            aggregateField4,
            aggregateField5,
            aggregateField6,
            aggregateField7,
            aggregateField8,
            aggregateField9,
            aggregateField10,
            aggregateField11,
            aggregateField12,
            aggregateField13,
            aggregateField14,
            aggregateField15,
            aggregateField16,
            aggregateField17,
            aggregateField18,
            aggregateField19,
            aggregateField20,
            aggregateField21,
            aggregateField22,
            aggregateField23,
            aggregateField24,
            aggregateField25,
            aggregateField26,
            aggregateField27,
            aggregateField28,
            aggregateField29,
            aggregateField30,
          ],
        ),
        returnValue: _FakeAggregateQuery_10(
          this,
          Invocation.method(
            #aggregate,
            [
              aggregateField1,
              aggregateField2,
              aggregateField3,
              aggregateField4,
              aggregateField5,
              aggregateField6,
              aggregateField7,
              aggregateField8,
              aggregateField9,
              aggregateField10,
              aggregateField11,
              aggregateField12,
              aggregateField13,
              aggregateField14,
              aggregateField15,
              aggregateField16,
              aggregateField17,
              aggregateField18,
              aggregateField19,
              aggregateField20,
              aggregateField21,
              aggregateField22,
              aggregateField23,
              aggregateField24,
              aggregateField25,
              aggregateField26,
              aggregateField27,
              aggregateField28,
              aggregateField29,
              aggregateField30,
            ],
          ),
        ),
      ) as _i4.AggregateQuery);
}

/// A class which mocks [DocumentReference].
///
/// See the documentation for Mockito's code generation for more information.
// ignore: must_be_immutable
class MockDocumentReference<T extends Object?> extends _i1.Mock
    implements _i4.DocumentReference<T> {
  MockDocumentReference() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.FirebaseFirestore get firestore => (super.noSuchMethod(
        Invocation.getter(#firestore),
        returnValue: _FakeFirebaseFirestore_9(
          this,
          Invocation.getter(#firestore),
        ),
      ) as _i4.FirebaseFirestore);

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  _i4.CollectionReference<T> get parent => (super.noSuchMethod(
        Invocation.getter(#parent),
        returnValue: _FakeCollectionReference_2<T>(
          this,
          Invocation.getter(#parent),
        ),
      ) as _i4.CollectionReference<T>);

  @override
  String get path => (super.noSuchMethod(
        Invocation.getter(#path),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#path),
        ),
      ) as String);

  @override
  _i4.CollectionReference<Map<String, dynamic>> collection(
          String? collectionPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #collection,
          [collectionPath],
        ),
        returnValue: _FakeCollectionReference_2<Map<String, dynamic>>(
          this,
          Invocation.method(
            #collection,
            [collectionPath],
          ),
        ),
      ) as _i4.CollectionReference<Map<String, dynamic>>);

  @override
  _i5.Future<void> delete() => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> update(Map<Object, Object?>? data) => (super.noSuchMethod(
        Invocation.method(
          #update,
          [data],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<_i4.DocumentSnapshot<T>> get([_i3.GetOptions? options]) =>
      (super.noSuchMethod(
        Invocation.method(
          #get,
          [options],
        ),
        returnValue: _i5.Future<_i4.DocumentSnapshot<T>>.value(
            _FakeDocumentSnapshot_11<T>(
          this,
          Invocation.method(
            #get,
            [options],
          ),
        )),
      ) as _i5.Future<_i4.DocumentSnapshot<T>>);

  @override
  _i5.Stream<_i4.DocumentSnapshot<T>> snapshots({
    bool? includeMetadataChanges = false,
    _i3.ListenSource? source = _i3.ListenSource.defaultSource,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #snapshots,
          [],
          {
            #includeMetadataChanges: includeMetadataChanges,
            #source: source,
          },
        ),
        returnValue: _i5.Stream<_i4.DocumentSnapshot<T>>.empty(),
      ) as _i5.Stream<_i4.DocumentSnapshot<T>>);

  @override
  _i5.Future<void> set(
    T? data, [
    _i3.SetOptions? options,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #set,
          [
            data,
            options,
          ],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i4.DocumentReference<R> withConverter<R>({
    required _i4.FromFirestore<R>? fromFirestore,
    required _i4.ToFirestore<R>? toFirestore,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #withConverter,
          [],
          {
            #fromFirestore: fromFirestore,
            #toFirestore: toFirestore,
          },
        ),
        returnValue: _FakeDocumentReference_7<R>(
          this,
          Invocation.method(
            #withConverter,
            [],
            {
              #fromFirestore: fromFirestore,
              #toFirestore: toFirestore,
            },
          ),
        ),
      ) as _i4.DocumentReference<R>);
}

/// A class which mocks [DocumentSnapshot].
///
/// See the documentation for Mockito's code generation for more information.
class MockDocumentSnapshot<T extends Object?> extends _i1.Mock
    implements _i4.DocumentSnapshot<T> {
  MockDocumentSnapshot() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  _i4.DocumentReference<T> get reference => (super.noSuchMethod(
        Invocation.getter(#reference),
        returnValue: _FakeDocumentReference_7<T>(
          this,
          Invocation.getter(#reference),
        ),
      ) as _i4.DocumentReference<T>);

  @override
  _i4.SnapshotMetadata get metadata => (super.noSuchMethod(
        Invocation.getter(#metadata),
        returnValue: _FakeSnapshotMetadata_12(
          this,
          Invocation.getter(#metadata),
        ),
      ) as _i4.SnapshotMetadata);

  @override
  bool get exists => (super.noSuchMethod(
        Invocation.getter(#exists),
        returnValue: false,
      ) as bool);

  @override
  dynamic get(Object? field) => super.noSuchMethod(Invocation.method(
        #get,
        [field],
      ));

  @override
  dynamic operator [](Object? field) => super.noSuchMethod(Invocation.method(
        #[],
        [field],
      ));
}

/// A class which mocks [FirebaseStorage].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseStorage extends _i1.Mock implements _i6.FirebaseStorage {
  MockFirebaseStorage() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app => (super.noSuchMethod(
        Invocation.getter(#app),
        returnValue: _FakeFirebaseApp_0(
          this,
          Invocation.getter(#app),
        ),
      ) as _i2.FirebaseApp);

  @override
  String get bucket => (super.noSuchMethod(
        Invocation.getter(#bucket),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#bucket),
        ),
      ) as String);

  @override
  Duration get maxOperationRetryTime => (super.noSuchMethod(
        Invocation.getter(#maxOperationRetryTime),
        returnValue: _FakeDuration_13(
          this,
          Invocation.getter(#maxOperationRetryTime),
        ),
      ) as Duration);

  @override
  Duration get maxUploadRetryTime => (super.noSuchMethod(
        Invocation.getter(#maxUploadRetryTime),
        returnValue: _FakeDuration_13(
          this,
          Invocation.getter(#maxUploadRetryTime),
        ),
      ) as Duration);

  @override
  Duration get maxDownloadRetryTime => (super.noSuchMethod(
        Invocation.getter(#maxDownloadRetryTime),
        returnValue: _FakeDuration_13(
          this,
          Invocation.getter(#maxDownloadRetryTime),
        ),
      ) as Duration);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
        Invocation.setter(
          #app,
          _app,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set bucket(String? _bucket) => super.noSuchMethod(
        Invocation.setter(
          #bucket,
          _bucket,
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<dynamic, dynamic> get pluginConstants => (super.noSuchMethod(
        Invocation.getter(#pluginConstants),
        returnValue: <dynamic, dynamic>{},
      ) as Map<dynamic, dynamic>);

  @override
  _i6.Reference ref([String? path]) => (super.noSuchMethod(
        Invocation.method(
          #ref,
          [path],
        ),
        returnValue: _FakeReference_14(
          this,
          Invocation.method(
            #ref,
            [path],
          ),
        ),
      ) as _i6.Reference);

  @override
  _i6.Reference refFromURL(String? url) => (super.noSuchMethod(
        Invocation.method(
          #refFromURL,
          [url],
        ),
        returnValue: _FakeReference_14(
          this,
          Invocation.method(
            #refFromURL,
            [url],
          ),
        ),
      ) as _i6.Reference);

  @override
  void setMaxOperationRetryTime(Duration? time) => super.noSuchMethod(
        Invocation.method(
          #setMaxOperationRetryTime,
          [time],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setMaxUploadRetryTime(Duration? time) => super.noSuchMethod(
        Invocation.method(
          #setMaxUploadRetryTime,
          [time],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setMaxDownloadRetryTime(Duration? time) => super.noSuchMethod(
        Invocation.method(
          #setMaxDownloadRetryTime,
          [time],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<void> useEmulator({
    required String? host,
    required int? port,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #useEmulator,
          [],
          {
            #host: host,
            #port: port,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> useStorageEmulator(
    String? host,
    int? port, {
    bool? automaticHostMapping = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #useStorageEmulator,
          [
            host,
            port,
          ],
          {#automaticHostMapping: automaticHostMapping},
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);
}

/// A class which mocks [Reference].
///
/// See the documentation for Mockito's code generation for more information.
class MockReference extends _i1.Mock implements _i6.Reference {
  MockReference() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.FirebaseStorage get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeFirebaseStorage_15(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i6.FirebaseStorage);

  @override
  String get bucket => (super.noSuchMethod(
        Invocation.getter(#bucket),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#bucket),
        ),
      ) as String);

  @override
  String get fullPath => (super.noSuchMethod(
        Invocation.getter(#fullPath),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#fullPath),
        ),
      ) as String);

  @override
  String get name => (super.noSuchMethod(
        Invocation.getter(#name),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#name),
        ),
      ) as String);

  @override
  _i6.Reference get root => (super.noSuchMethod(
        Invocation.getter(#root),
        returnValue: _FakeReference_14(
          this,
          Invocation.getter(#root),
        ),
      ) as _i6.Reference);

  @override
  _i6.Reference child(String? path) => (super.noSuchMethod(
        Invocation.method(
          #child,
          [path],
        ),
        returnValue: _FakeReference_14(
          this,
          Invocation.method(
            #child,
            [path],
          ),
        ),
      ) as _i6.Reference);

  @override
  _i5.Future<void> delete() => (super.noSuchMethod(
        Invocation.method(
          #delete,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<String> getDownloadURL() => (super.noSuchMethod(
        Invocation.method(
          #getDownloadURL,
          [],
        ),
        returnValue: _i5.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getDownloadURL,
            [],
          ),
        )),
      ) as _i5.Future<String>);

  @override
  _i5.Future<_i7.FullMetadata> getMetadata() => (super.noSuchMethod(
        Invocation.method(
          #getMetadata,
          [],
        ),
        returnValue: _i5.Future<_i7.FullMetadata>.value(_FakeFullMetadata_16(
          this,
          Invocation.method(
            #getMetadata,
            [],
          ),
        )),
      ) as _i5.Future<_i7.FullMetadata>);

  @override
  _i5.Future<_i6.ListResult> list([_i7.ListOptions? options]) =>
      (super.noSuchMethod(
        Invocation.method(
          #list,
          [options],
        ),
        returnValue: _i5.Future<_i6.ListResult>.value(_FakeListResult_17(
          this,
          Invocation.method(
            #list,
            [options],
          ),
        )),
      ) as _i5.Future<_i6.ListResult>);

  @override
  _i5.Future<_i6.ListResult> listAll() => (super.noSuchMethod(
        Invocation.method(
          #listAll,
          [],
        ),
        returnValue: _i5.Future<_i6.ListResult>.value(_FakeListResult_17(
          this,
          Invocation.method(
            #listAll,
            [],
          ),
        )),
      ) as _i5.Future<_i6.ListResult>);

  @override
  _i5.Future<_i9.Uint8List?> getData([int? maxSize = 10485760]) =>
      (super.noSuchMethod(
        Invocation.method(
          #getData,
          [maxSize],
        ),
        returnValue: _i5.Future<_i9.Uint8List?>.value(),
      ) as _i5.Future<_i9.Uint8List?>);

  @override
  _i6.UploadTask putData(
    _i9.Uint8List? data, [
    _i7.SettableMetadata? metadata,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #putData,
          [
            data,
            metadata,
          ],
        ),
        returnValue: _FakeUploadTask_18(
          this,
          Invocation.method(
            #putData,
            [
              data,
              metadata,
            ],
          ),
        ),
      ) as _i6.UploadTask);

  @override
  _i6.UploadTask putBlob(
    dynamic blob, [
    _i7.SettableMetadata? metadata,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #putBlob,
          [
            blob,
            metadata,
          ],
        ),
        returnValue: _FakeUploadTask_18(
          this,
          Invocation.method(
            #putBlob,
            [
              blob,
              metadata,
            ],
          ),
        ),
      ) as _i6.UploadTask);

  @override
  _i6.UploadTask putFile(
    _i10.File? file, [
    _i7.SettableMetadata? metadata,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #putFile,
          [
            file,
            metadata,
          ],
        ),
        returnValue: _FakeUploadTask_18(
          this,
          Invocation.method(
            #putFile,
            [
              file,
              metadata,
            ],
          ),
        ),
      ) as _i6.UploadTask);

  @override
  _i6.UploadTask putString(
    String? data, {
    _i7.PutStringFormat? format = _i7.PutStringFormat.raw,
    _i7.SettableMetadata? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #putString,
          [data],
          {
            #format: format,
            #metadata: metadata,
          },
        ),
        returnValue: _FakeUploadTask_18(
          this,
          Invocation.method(
            #putString,
            [data],
            {
              #format: format,
              #metadata: metadata,
            },
          ),
        ),
      ) as _i6.UploadTask);

  @override
  _i5.Future<_i7.FullMetadata> updateMetadata(_i7.SettableMetadata? metadata) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateMetadata,
          [metadata],
        ),
        returnValue: _i5.Future<_i7.FullMetadata>.value(_FakeFullMetadata_16(
          this,
          Invocation.method(
            #updateMetadata,
            [metadata],
          ),
        )),
      ) as _i5.Future<_i7.FullMetadata>);

  @override
  _i6.DownloadTask writeToFile(_i10.File? file) => (super.noSuchMethod(
        Invocation.method(
          #writeToFile,
          [file],
        ),
        returnValue: _FakeDownloadTask_19(
          this,
          Invocation.method(
            #writeToFile,
            [file],
          ),
        ),
      ) as _i6.DownloadTask);
}

/// A class which mocks [UploadTask].
///
/// See the documentation for Mockito's code generation for more information.
class MockUploadTask extends _i1.Mock implements _i6.UploadTask {
  MockUploadTask() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.FirebaseStorage get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeFirebaseStorage_15(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i6.FirebaseStorage);

  @override
  _i5.Stream<_i6.TaskSnapshot> get snapshotEvents => (super.noSuchMethod(
        Invocation.getter(#snapshotEvents),
        returnValue: _i5.Stream<_i6.TaskSnapshot>.empty(),
      ) as _i5.Stream<_i6.TaskSnapshot>);

  @override
  _i6.TaskSnapshot get snapshot => (super.noSuchMethod(
        Invocation.getter(#snapshot),
        returnValue: _FakeTaskSnapshot_20(
          this,
          Invocation.getter(#snapshot),
        ),
      ) as _i6.TaskSnapshot);

  @override
  _i5.Future<bool> pause() => (super.noSuchMethod(
        Invocation.method(
          #pause,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> resume() => (super.noSuchMethod(
        Invocation.method(
          #resume,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<bool> cancel() => (super.noSuchMethod(
        Invocation.method(
          #cancel,
          [],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Stream<_i6.TaskSnapshot> asStream() => (super.noSuchMethod(
        Invocation.method(
          #asStream,
          [],
        ),
        returnValue: _i5.Stream<_i6.TaskSnapshot>.empty(),
      ) as _i5.Stream<_i6.TaskSnapshot>);

  @override
  _i5.Future<_i6.TaskSnapshot> catchError(
    Function? onError, {
    bool Function(Object)? test,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #catchError,
          [onError],
          {#test: test},
        ),
        returnValue: _i5.Future<_i6.TaskSnapshot>.value(_FakeTaskSnapshot_20(
          this,
          Invocation.method(
            #catchError,
            [onError],
            {#test: test},
          ),
        )),
      ) as _i5.Future<_i6.TaskSnapshot>);

  @override
  _i5.Future<S> then<S>(
    _i5.FutureOr<S> Function(_i6.TaskSnapshot)? onValue, {
    Function? onError,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #then,
          [onValue],
          {#onError: onError},
        ),
        returnValue: _i8.ifNotNull(
              _i8.dummyValueOrNull<S>(
                this,
                Invocation.method(
                  #then,
                  [onValue],
                  {#onError: onError},
                ),
              ),
              (S v) => _i5.Future<S>.value(v),
            ) ??
            _FakeFuture_8<S>(
              this,
              Invocation.method(
                #then,
                [onValue],
                {#onError: onError},
              ),
            ),
      ) as _i5.Future<S>);

  @override
  _i5.Future<_i6.TaskSnapshot> whenComplete(
          _i5.FutureOr<dynamic> Function()? action) =>
      (super.noSuchMethod(
        Invocation.method(
          #whenComplete,
          [action],
        ),
        returnValue: _i5.Future<_i6.TaskSnapshot>.value(_FakeTaskSnapshot_20(
          this,
          Invocation.method(
            #whenComplete,
            [action],
          ),
        )),
      ) as _i5.Future<_i6.TaskSnapshot>);

  @override
  _i5.Future<_i6.TaskSnapshot> timeout(
    Duration? timeLimit, {
    _i5.FutureOr<_i6.TaskSnapshot> Function()? onTimeout,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #timeout,
          [timeLimit],
          {#onTimeout: onTimeout},
        ),
        returnValue: _i5.Future<_i6.TaskSnapshot>.value(_FakeTaskSnapshot_20(
          this,
          Invocation.method(
            #timeout,
            [timeLimit],
            {#onTimeout: onTimeout},
          ),
        )),
      ) as _i5.Future<_i6.TaskSnapshot>);
}

/// A class which mocks [TaskSnapshot].
///
/// See the documentation for Mockito's code generation for more information.
class MockTaskSnapshot extends _i1.Mock implements _i6.TaskSnapshot {
  MockTaskSnapshot() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.FirebaseStorage get storage => (super.noSuchMethod(
        Invocation.getter(#storage),
        returnValue: _FakeFirebaseStorage_15(
          this,
          Invocation.getter(#storage),
        ),
      ) as _i6.FirebaseStorage);

  @override
  int get bytesTransferred => (super.noSuchMethod(
        Invocation.getter(#bytesTransferred),
        returnValue: 0,
      ) as int);

  @override
  _i6.Reference get ref => (super.noSuchMethod(
        Invocation.getter(#ref),
        returnValue: _FakeReference_14(
          this,
          Invocation.getter(#ref),
        ),
      ) as _i6.Reference);

  @override
  _i7.TaskState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _i7.TaskState.paused,
      ) as _i7.TaskState);

  @override
  int get totalBytes => (super.noSuchMethod(
        Invocation.getter(#totalBytes),
        returnValue: 0,
      ) as int);
}
