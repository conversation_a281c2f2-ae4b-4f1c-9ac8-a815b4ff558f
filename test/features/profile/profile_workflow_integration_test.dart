import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../lib/features/profile/data/profile_service.dart';
import '../../../lib/core/models/user_model.dart';

// Generate mocks
@GenerateMocks([
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  FirebaseStorage,
])
import 'profile_workflow_integration_test.mocks.dart';

void main() {
  group('Profile Management Workflow Integration Tests', () {
    late ProfileService profileService;
    late MockFirebaseFirestore mockFirestore;
    late MockFirebaseStorage mockStorage;
    late MockCollectionReference<Map<String, dynamic>> mockCollection;
    late MockDocumentReference<Map<String, dynamic>> mockDocument;
    late MockDocumentSnapshot<Map<String, dynamic>> mockSnapshot;

    setUp(() {
      mockFirestore = MockFirebaseFirestore();
      mockStorage = MockFirebaseStorage();
      mockCollection = MockCollectionReference<Map<String, dynamic>>();
      mockDocument = MockDocumentReference<Map<String, dynamic>>();
      mockSnapshot = MockDocumentSnapshot<Map<String, dynamic>>();
      
      profileService = ProfileService(mockFirestore, mockStorage);
    });

    group('Complete Profile Management Workflow', () {
      test('should handle complete profile creation and update workflow', () async {
        // Arrange
        const userId = 'test-user-id';
        
        // Initial profile data
        final initialUserModel = UserModel(
          id: userId,
          email: '<EMAIL>',
          fullName: 'Test User',
          createdAt: DateTime.now(),
        );

        // Updated profile data
        final updatedUserModel = initialUserModel.copyWith(
          phoneNumber: '+1234567890',
          campus: 'Test University',
          preferences: const UserPreferences(
            maxBudget: 75000.0,
            receiveNotifications: true,
            preferredCurrency: 'NGN',
          ),
        );

        // Mock Firestore operations
        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});
        when(mockDocument.get()).thenAnswer((_) async => mockSnapshot);
        when(mockSnapshot.exists).thenReturn(true);
        when(mockSnapshot.data()).thenReturn({
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'phoneNumber': '+1234567890',
          'campus': 'Test University',
          'createdAt': DateTime.now().toIso8601String(),
          'isVerified': false,
          'role': 'student',
          'preferences': {
            'maxBudget': 75000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        });

        // Act - Create initial profile
        await profileService.updateUserProfile(userId, initialUserModel);
        
        // Act - Update profile with complete information
        await profileService.updateUserProfile(userId, updatedUserModel);
        
        // Act - Retrieve updated profile
        final retrievedProfile = await profileService.getUserProfile(userId);

        // Assert
        expect(retrievedProfile, isNotNull);
        expect(retrievedProfile!.id, equals(userId));
        expect(retrievedProfile.email, equals('<EMAIL>'));
        expect(retrievedProfile.fullName, equals('Test User'));
        expect(retrievedProfile.phoneNumber, equals('+1234567890'));
        expect(retrievedProfile.campus, equals('Test University'));
        expect(retrievedProfile.preferences.maxBudget, equals(75000.0));
        expect(retrievedProfile.preferences.receiveNotifications, isTrue);
        expect(retrievedProfile.hasCompleteProfile, isTrue);
        
        // Verify Firestore operations
        verify(mockFirestore.collection('users')).called(3); // 2 updates + 1 get
        verify(mockCollection.doc(userId)).called(3);
        verify(mockDocument.update(any)).called(2); // 2 updates
        verify(mockDocument.get()).called(1); // 1 retrieval
      });

      test('should validate profile data throughout the workflow', () async {
        // Arrange
        const userId = 'test-user-id';
        
        // Valid profile
        final validUserModel = UserModel(
          id: userId,
          email: '<EMAIL>',
          fullName: 'Valid User',
          phoneNumber: '+1234567890',
          campus: 'Valid University',
          createdAt: DateTime.now(),
        );

        // Invalid profile
        final invalidUserModel = UserModel(
          id: '',  // Invalid empty ID
          email: 'invalid-email',  // Invalid email format
          fullName: '',  // Invalid empty name
          createdAt: DateTime.now(),
        );

        // Mock Firestore operations for valid profile
        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act & Assert - Valid profile should succeed
        await profileService.updateUserProfile(userId, validUserModel);
        
        // Act & Assert - Invalid profile should fail
        expect(
          () => profileService.updateUserProfile(userId, invalidUserModel),
          throwsA(isA<ProfileException>()),
        );
        
        // Verify only valid profile was processed
        verify(mockDocument.update(any)).called(1);
      });

      test('should handle profile field updates correctly', () async {
        // Arrange
        const userId = 'test-user-id';
        final fieldsToUpdate = {
          'fullName': 'Updated Name',
          'phoneNumber': '+9876543210',
          'campus': 'Updated University',
          'preferences.receiveNotifications': false,
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act
        await profileService.updateProfileFields(userId, fieldsToUpdate);

        // Assert
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should provide real-time profile updates', () async {
        // Arrange
        const userId = 'test-user-id';
        final userData = {
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'phoneNumber': '+1234567890',
          'campus': 'Test University',
          'createdAt': DateTime.now().toIso8601String(),
          'isVerified': false,
          'role': 'student',
          'preferences': {
            'maxBudget': 50000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.snapshots()).thenAnswer(
          (_) => Stream.fromIterable([mockSnapshot]),
        );
        when(mockSnapshot.exists).thenReturn(true);
        when(mockSnapshot.data()).thenReturn(userData);

        // Act
        final stream = profileService.streamUserProfile(userId);
        final result = await stream.first;

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(userId));
        expect(result.fullName, equals('Test User'));
        expect(result.phoneNumber, equals('+1234567890'));
        expect(result.campus, equals('Test University'));
        
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.snapshots()).called(1);
      });

      test('should handle image upload functionality', () {
        // Test that all required image methods are available
        expect(profileService.pickImageFromGallery, isA<Function>());
        expect(profileService.pickImageFromCamera, isA<Function>());
        expect(profileService.uploadProfileImage, isA<Function>());
        expect(profileService.updateProfileImage, isA<Function>());
        expect(profileService.deleteProfileImage, isA<Function>());
      });
    });

    group('Profile Management Error Handling', () {
      test('should handle network errors gracefully', () async {
        // Arrange
        const userId = 'test-user-id';

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => profileService.getUserProfile(userId),
          throwsA(isA<ProfileException>()),
        );
      });

      test('should handle validation errors appropriately', () async {
        // Arrange
        const userId = 'test-user-id';
        final invalidUser = UserModel(
          id: '',
          email: 'invalid',
          fullName: '',
          createdAt: DateTime.now(),
        );

        // Act & Assert
        expect(
          () => profileService.updateUserProfile(userId, invalidUser),
          throwsA(isA<ProfileException>()),
        );
      });
    });
  });
}