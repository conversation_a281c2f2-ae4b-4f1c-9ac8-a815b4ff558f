import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import '../../../lib/features/profile/data/profile_service.dart';
import '../../../lib/core/models/user_model.dart';

// Generate mocks
@GenerateMocks([
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  FirebaseStorage,
  Reference,
  UploadTask,
  TaskSnapshot,
])
import 'profile_complete_integration_test.mocks.dart';

void main() {
  group('Profile Management Integration Tests', () {
    late ProfileService profileService;
    late MockFirebaseFirestore mockFirestore;
    late MockFirebaseStorage mockStorage;
    late MockCollectionReference<Map<String, dynamic>> mockCollection;
    late MockDocumentReference<Map<String, dynamic>> mockDocument;
    late MockDocumentSnapshot<Map<String, dynamic>> mockSnapshot;

    setUp(() {
      mockFirestore = MockFirebaseFirestore();
      mockStorage = MockFirebaseStorage();
      mockCollection = MockCollectionReference<Map<String, dynamic>>();
      mockDocument = MockDocumentReference<Map<String, dynamic>>();
      mockSnapshot = MockDocumentSnapshot<Map<String, dynamic>>();
      
      profileService = ProfileService(mockFirestore, mockStorage);
    });

    group('Profile Data Persistence', () {
      test('should persist user profile data to Firestore successfully', () async {
        // Arrange
        const userId = 'test-user-id';
        final userModel = UserModel(
          id: userId,
          email: '<EMAIL>',
          fullName: 'Test User',
          phoneNumber: '+1234567890',
          campus: 'Test University',
          createdAt: DateTime.now(),
          preferences: const UserPreferences(
            maxBudget: 75000.0,
            receiveNotifications: true,
            preferredCurrency: 'NGN',
          ),
        );

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act
        await profileService.updateUserProfile(userId, userModel);

        // Assert
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should retrieve user profile data from Firestore successfully', () async {
        // Arrange
        const userId = 'test-user-id';
        final userData = {
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'phoneNumber': '+1234567890',
          'campus': 'Test University',
          'profileImageUrl': 'https://example.com/image.jpg',
          'createdAt': DateTime.now().toIso8601String(),
          'isVerified': false,
          'role': 'student',
          'preferences': {
            'maxBudget': 75000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async => mockSnapshot);
        when(mockSnapshot.exists).thenReturn(true);
        when(mockSnapshot.data()).thenReturn(userData);

        // Act
        final result = await profileService.getUserProfile(userId);

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(userId));
        expect(result.email, equals('<EMAIL>'));
        expect(result.fullName, equals('Test User'));
        expect(result.phoneNumber, equals('+1234567890'));
        expect(result.campus, equals('Test University'));
        expect(result.preferences.maxBudget, equals(75000.0));
        expect(result.preferences.receiveNotifications, isTrue);
      });

      test('should handle profile data validation during persistence', () async {
        // Arrange
        const userId = 'test-user-id';
        final invalidUserModel = UserModel(
          id: '',  // Invalid empty ID
          email: 'invalid-email',  // Invalid email format
          fullName: '',  // Invalid empty name
          createdAt: DateTime.now(),
        );

        // Act & Assert
        expect(
          () => profileService.updateUserProfile(userId, invalidUserModel),
          throwsA(isA<ProfileException>()),
        );
      });
    });

    group('Profile Field Updates', () {
      test('should update specific profile fields successfully', () async {
        // Arrange
        const userId = 'test-user-id';
        final fieldsToUpdate = {
          'fullName': 'Updated Full Name',
          'phoneNumber': '+9876543210',
          'campus': 'Updated University',
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act
        await profileService.updateProfileFields(userId, fieldsToUpdate);

        // Assert
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should update notification preferences successfully', () async {
        // Arrange
        const userId = 'test-user-id';
        final preferencesUpdate = {
          'preferences.receiveNotifications': false,
          'preferences.maxBudget': 100000.0,
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act
        await profileService.updateProfileFields(userId, preferencesUpdate);

        // Assert
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.update(any)).called(1);
      });
    });

    group('Profile Image Management', () {
      test('should handle image picker operations', () async {
        // Test image picker functionality
        // Note: This tests the service methods, actual image picking requires UI interaction
        
        // Test that the service has the required methods
        expect(profileService.pickImageFromGallery, isA<Function>());
        expect(profileService.pickImageFromCamera, isA<Function>());
      });

      test('should validate image upload requirements', () async {
        // Arrange
        const userId = 'test-user-id';
        
        // Test that upload method exists and can handle XFile
        expect(profileService.uploadProfileImage, isA<Function>());
        expect(profileService.updateProfileImage, isA<Function>());
        expect(profileService.deleteProfileImage, isA<Function>());
      });
    });

    group('Real-time Profile Updates', () {
      test('should stream profile changes in real-time', () async {
        // Arrange
        const userId = 'test-user-id';
        final userData = {
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'createdAt': DateTime.now().toIso8601String(),
          'isVerified': false,
          'role': 'student',
          'preferences': {
            'maxBudget': 50000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.snapshots()).thenAnswer(
          (_) => Stream.fromIterable([mockSnapshot]),
        );
        when(mockSnapshot.exists).thenReturn(true);
        when(mockSnapshot.data()).thenReturn(userData);

        // Act
        final stream = profileService.streamUserProfile(userId);
        final result = await stream.first;

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(userId));
        expect(result.email, equals('<EMAIL>'));
        expect(result.fullName, equals('Test User'));
        
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.snapshots()).called(1);
      });

      test('should handle profile stream when user does not exist', () async {
        // Arrange
        const userId = 'non-existent-user';

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.snapshots()).thenAnswer(
          (_) => Stream.fromIterable([mockSnapshot]),
        );
        when(mockSnapshot.exists).thenReturn(false);

        // Act
        final stream = profileService.streamUserProfile(userId);
        final result = await stream.first;

        // Assert
        expect(result, isNull);
      });
    });

    group('Error Handling', () {
      test('should handle Firestore connection errors gracefully', () async {
        // Arrange
        const userId = 'test-user-id';

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () => profileService.getUserProfile(userId),
          throwsA(isA<ProfileException>()),
        );
      });

      test('should handle profile update failures gracefully', () async {
        // Arrange
        const userId = 'test-user-id';
        final userModel = UserModel(
          id: userId,
          email: '<EMAIL>',
          fullName: 'Test User',
          createdAt: DateTime.now(),
        );

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenThrow(Exception('Update failed'));

        // Act & Assert
        expect(
          () => profileService.updateUserProfile(userId, userModel),
          throwsA(isA<ProfileException>()),
        );
      });

      test('should handle field update failures gracefully', () async {
        // Arrange
        const userId = 'test-user-id';
        final fieldsToUpdate = {'fullName': 'Updated Name'};

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenThrow(Exception('Update failed'));

        // Act & Assert
        expect(
          () => profileService.updateProfileFields(userId, fieldsToUpdate),
          throwsA(isA<ProfileException>()),
        );
      });
    });

    group('Profile Validation', () {
      test('should validate user model fields correctly', () {
        // Test valid user model
        final validUser = UserModel(
          id: 'valid-id',
          email: '<EMAIL>',
          fullName: 'Valid User',
          phoneNumber: '+1234567890',
          campus: 'Valid University',
          createdAt: DateTime.now(),
        );

        expect(validUser.isValid, isTrue);
        expect(validUser.validate(), isEmpty);
      });

      test('should detect invalid user model fields', () {
        // Test invalid user model
        final invalidUser = UserModel(
          id: '',  // Invalid empty ID
          email: 'invalid-email',  // Invalid email format
          fullName: '',  // Invalid empty name
          phoneNumber: '123',  // Invalid phone number
          createdAt: DateTime.now(),
        );

        expect(invalidUser.isValid, isFalse);
        expect(invalidUser.validate(), isNotEmpty);
        expect(invalidUser.validate().length, greaterThan(2));
      });

      test('should validate phone number formats correctly', () {
        // Test valid phone numbers
        final validPhoneUser = UserModel(
          id: 'test-id',
          email: '<EMAIL>',
          fullName: 'Test User',
          phoneNumber: '+1234567890',
          createdAt: DateTime.now(),
        );

        expect(validPhoneUser.isValid, isTrue);

        // Test invalid phone number
        final invalidPhoneUser = UserModel(
          id: 'test-id',
          email: '<EMAIL>',
          fullName: 'Test User',
          phoneNumber: '123',  // Too short
          createdAt: DateTime.now(),
        );

        expect(invalidPhoneUser.isValid, isFalse);
      });

      test('should validate email formats correctly', () {
        // Test valid email
        final validEmailUser = UserModel(
          id: 'test-id',
          email: '<EMAIL>',
          fullName: 'Test User',
          createdAt: DateTime.now(),
        );

        expect(validEmailUser.isValid, isTrue);

        // Test invalid email
        final invalidEmailUser = UserModel(
          id: 'test-id',
          email: 'invalid-email',
          fullName: 'Test User',
          createdAt: DateTime.now(),
        );

        expect(invalidEmailUser.isValid, isFalse);
      });
    });

    group('Profile Completion Status', () {
      test('should correctly identify complete profiles', () {
        final completeUser = UserModel(
          id: 'test-id',
          email: '<EMAIL>',
          fullName: 'Complete User',
          phoneNumber: '+1234567890',
          campus: 'Test University',
          createdAt: DateTime.now(),
        );

        expect(completeUser.hasCompleteProfile, isTrue);
      });

      test('should correctly identify incomplete profiles', () {
        final incompleteUser = UserModel(
          id: 'test-id',
          email: '<EMAIL>',
          fullName: 'Incomplete User',
          // Missing phoneNumber and campus
          createdAt: DateTime.now(),
        );

        expect(incompleteUser.hasCompleteProfile, isFalse);
      });
    });
  });
}