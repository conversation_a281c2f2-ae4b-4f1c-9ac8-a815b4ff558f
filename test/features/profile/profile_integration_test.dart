import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../../../lib/features/profile/data/profile_service.dart';
import '../../../lib/core/models/user_model.dart';

// Generate mocks
@GenerateMocks([
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  FirebaseStorage,
  Reference,
  UploadTask,
  TaskSnapshot,
])
import 'profile_integration_test.mocks.dart';

void main() {
  group('ProfileService Integration Tests', () {
    late ProfileService profileService;
    late MockFirebaseFirestore mockFirestore;
    late MockFirebaseStorage mockStorage;
    late MockCollectionReference<Map<String, dynamic>> mockCollection;
    late MockDocumentReference<Map<String, dynamic>> mockDocument;
    late MockDocumentSnapshot<Map<String, dynamic>> mockSnapshot;

    setUp(() {
      mockFirestore = MockFirebaseFirestore();
      mockStorage = MockFirebaseStorage();
      mockCollection = MockCollectionReference<Map<String, dynamic>>();
      mockDocument = MockDocumentReference<Map<String, dynamic>>();
      mockSnapshot = MockDocumentSnapshot<Map<String, dynamic>>();

      profileService = ProfileService(mockFirestore, mockStorage);
    });

    group('getUserProfile', () {
      test('should return user profile when document exists', () async {
        // Arrange
        const userId = 'test-user-id';
        final userData = {
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'phoneNumber': '+1234567890',
          'campus': 'Test University',
          'profileImageUrl': 'https://example.com/image.jpg',
          'createdAt': DateTime.now().toIso8601String(),
          'isVerified': false,
          'role': 'student',
          'preferences': {
            'maxBudget': 50000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async => mockSnapshot);
        when(mockSnapshot.exists).thenReturn(true);
        when(mockSnapshot.data()).thenReturn(userData);

        // Act
        final result = await profileService.getUserProfile(userId);

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(userId));
        expect(result.email, equals('<EMAIL>'));
        expect(result.fullName, equals('Test User'));
        expect(result.phoneNumber, equals('+1234567890'));
        expect(result.campus, equals('Test University'));

        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.get()).called(1);
      });

      test('should return null when document does not exist', () async {
        // Arrange
        const userId = 'non-existent-user';

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async => mockSnapshot);
        when(mockSnapshot.exists).thenReturn(false);

        // Act
        final result = await profileService.getUserProfile(userId);

        // Assert
        expect(result, isNull);

        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.get()).called(1);
      });

      test('should throw ProfileException when Firestore operation fails',
          () async {
        // Arrange
        const userId = 'test-user-id';

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.get()).thenThrow(Exception('Firestore error'));

        // Act & Assert
        expect(
          () => profileService.getUserProfile(userId),
          throwsA(isA<ProfileException>()),
        );
      });
    });

    group('updateUserProfile', () {
      test('should update user profile successfully', () async {
        // Arrange
        const userId = 'test-user-id';
        final userModel = UserModel(
          id: userId,
          email: '<EMAIL>',
          fullName: 'Updated User',
          phoneNumber: '+1234567890',
          campus: 'Updated University',
          createdAt: DateTime.now(),
        );

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act
        await profileService.updateUserProfile(userId, userModel);

        // Assert
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should throw ProfileException when validation fails', () async {
        // Arrange
        const userId = 'test-user-id';
        final invalidUserModel = UserModel(
          id: '', // Invalid empty ID
          email: 'invalid-email', // Invalid email format
          fullName: '', // Invalid empty name
          createdAt: DateTime.now(),
        );

        // Act & Assert
        expect(
          () => profileService.updateUserProfile(userId, invalidUserModel),
          throwsA(isA<ProfileException>()),
        );
      });

      test('should throw ProfileException when Firestore update fails',
          () async {
        // Arrange
        const userId = 'test-user-id';
        final userModel = UserModel(
          id: userId,
          email: '<EMAIL>',
          fullName: 'Test User',
          createdAt: DateTime.now(),
        );

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenThrow(Exception('Update failed'));

        // Act & Assert
        expect(
          () => profileService.updateUserProfile(userId, userModel),
          throwsA(isA<ProfileException>()),
        );
      });
    });

    group('updateProfileFields', () {
      test('should update specific profile fields successfully', () async {
        // Arrange
        const userId = 'test-user-id';
        final fieldsToUpdate = {
          'fullName': 'Updated Name',
          'phoneNumber': '+9876543210',
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Act
        await profileService.updateProfileFields(userId, fieldsToUpdate);

        // Assert
        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.update(any)).called(1);
      });

      test('should throw ProfileException when field update fails', () async {
        // Arrange
        const userId = 'test-user-id';
        final fieldsToUpdate = {'fullName': 'Updated Name'};

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenThrow(Exception('Update failed'));

        // Act & Assert
        expect(
          () => profileService.updateProfileFields(userId, fieldsToUpdate),
          throwsA(isA<ProfileException>()),
        );
      });
    });

    // Note: Image upload tests are skipped due to Firebase Storage mocking complexity
    // The image upload functionality is tested through manual testing and UI tests

    group('streamUserProfile', () {
      test('should return stream of user profile changes', () async {
        // Arrange
        const userId = 'test-user-id';
        final userData = {
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'createdAt': DateTime.now().toIso8601String(),
          'isVerified': false,
          'role': 'student',
          'preferences': {
            'maxBudget': 50000.0,
            'preferredAmenities': [],
            'preferredType': 'any',
            'maxDistanceFromCampus': 5.0,
            'receiveNotifications': true,
            'preferredCurrency': 'NGN',
          },
        };

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.snapshots()).thenAnswer(
          (_) => Stream.fromIterable([mockSnapshot]),
        );
        when(mockSnapshot.exists).thenReturn(true);
        when(mockSnapshot.data()).thenReturn(userData);

        // Act
        final stream = profileService.streamUserProfile(userId);
        final result = await stream.first;

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(userId));
        expect(result.email, equals('<EMAIL>'));
        expect(result.fullName, equals('Test User'));

        verify(mockFirestore.collection('users')).called(1);
        verify(mockCollection.doc(userId)).called(1);
        verify(mockDocument.snapshots()).called(1);
      });

      test('should return null when document does not exist in stream',
          () async {
        // Arrange
        const userId = 'non-existent-user';

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc(userId)).thenReturn(mockDocument);
        when(mockDocument.snapshots()).thenAnswer(
          (_) => Stream.fromIterable([mockSnapshot]),
        );
        when(mockSnapshot.exists).thenReturn(false);

        // Act
        final stream = profileService.streamUserProfile(userId);
        final result = await stream.first;

        // Assert
        expect(result, isNull);
      });
    });
  });
}
