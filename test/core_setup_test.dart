import 'package:flutter_test/flutter_test.dart';
import 'package:campus_find/core/config/app_config.dart';
import 'package:campus_find/core/config/environment.dart';
import 'package:campus_find/core/config/payment_config.dart';
import 'package:campus_find/core/config/maps_config.dart';

void main() {
  group('Core Setup Tests', () {
    test('Environment configuration should have default values', () {
      expect(Environment.isDevelopment, isTrue);
      expect(Environment.enableLogging, isTrue);
      expect(Environment.googleMapsApiKey, isNotEmpty);
      expect(Environment.paystackPublicKey, isNotEmpty);
      expect(Environment.stripePublishableKey, isNotEmpty);
    });

    test('Payment configuration should have supported currencies', () {
      expect(PaymentConfig.supportedCurrencies, contains('NGN'));
      expect(PaymentConfig.supportedCurrencies, contains('USD'));
      expect(PaymentConfig.supportedCurrencies, contains('GHS'));
      expect(PaymentConfig.supportedCurrencies, contains('KES'));
    });

    test('Payment configuration should format currency correctly', () {
      expect(PaymentConfig.getCurrencySymbol('NGN'), equals('₦'));
      expect(PaymentConfig.getCurrencySymbol('USD'), equals('\$'));
      expect(PaymentConfig.getCurrencySymbol('GHS'), equals('₵'));
      expect(PaymentConfig.getCurrencySymbol('KES'), equals('KSh'));
    });

    test('Payment configuration should format amounts correctly', () {
      expect(PaymentConfig.formatAmount(100.0, 'NGN'), equals('₦100.00'));
      expect(PaymentConfig.formatAmount(50.5, 'USD'), equals('\$50.50'));
    });

    test('Maps configuration should have default camera position', () {
      expect(MapsConfig.defaultCameraPosition.target.latitude, equals(6.5244));
      expect(MapsConfig.defaultCameraPosition.target.longitude, equals(3.3792));
      expect(MapsConfig.defaultCameraPosition.zoom, equals(14.0));
    });

    test('App configuration should provide app info', () {
      final appInfo = AppConfig.getAppInfo();
      expect(appInfo['name'], equals('Campus Find'));
      expect(appInfo['version'], equals('1.0.0'));
      expect(appInfo['environment'], equals('development'));
      expect(appInfo['payment_gateways'], contains('Stripe'));
      expect(appInfo['maps_provider'], equals('Google Maps'));
    });
  });
}