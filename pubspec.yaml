name: campus_find
description: A new Flutter project.

publish_to: 'none'

version: 0.1.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  
  # UI Components
  cupertino_icons: ^1.0.2
  
  # Firebase
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  
  # Code Generation
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  
  # Utilities
  shared_preferences: ^2.2.2
  image_picker: ^1.0.4
  
  # Maps
  google_maps_flutter: ^2.5.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  freezed: ^2.4.6
  json_serializable: ^6.7.1
  
  # Testing
  mockito: ^5.4.4

flutter:
  uses-material-design: true

  assets:
    - assets/images/

  fonts:
    - family: SourceSansPro
      fonts:
        - asset: assets/fonts/SourceSansPro-Regular.ttf
    - family: RobotoRegular
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
    - family: RobotoLight
      fonts:
        - asset: assets/fonts/Roboto-Light.ttf