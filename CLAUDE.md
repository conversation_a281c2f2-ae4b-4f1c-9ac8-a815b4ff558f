# Hostel Aggregation Platform

A comprehensive mobile platform for student accommodation discovery with pay-to-view contact information, built with Flutter and Supabase.

## 🎯 Project Overview

**Business Model**: Aggregation platform where students discover hostels and pay to access contact information
**Target Users**: University students seeking accommodation and property agents/landlords
**Revenue Model**: Pay-per-view contact information system

### Key Features
- 🏠 Hostel discovery with advanced filtering
- 💰 Pay-to-view contact information
- ⭐ Rating and review system
- 📍 Location-based search with maps
- 👥 Multi-user system (students, agents, admins)
- 🎓 Campus representatives program
- 💎 Premium featured listings
- 📱 Mobile-first responsive design

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Flutter (Dart)
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Edge Functions)
- **Database**: PostgreSQL with Row Level Security
- **Authentication**: Supabase Auth with email/phone verification
- **Payments**: Integration ready (Paystack/Flutterwave)
- **Maps**: Google Maps/Mapbox integration
- **State Management**: Riverpod/Bloc (specify which one you're using)

### Folder Structure (Feature-Based)
```
lib/
├── core/
│   ├── constants/
│   │   ├── app_constants.dart
│   │   ├── database_constants.dart
│   │   └── api_endpoints.dart
│   ├── config/
│   │   ├── supabase_config.dart
│   │   ├── app_config.dart
│   │   └── environment.dart
│   ├── theme/
│   │   ├── app_theme.dart
│   │   ├── colors.dart
│   │   └── text_styles.dart
│   ├── utils/
│   │   ├── validators.dart
│   │   ├── formatters.dart
│   │   └── helpers.dart
│   ├── error/
│   │   ├── exceptions.dart
│   │   ├── failures.dart
│   │   └── error_handler.dart
│   └── network/
│       ├── supabase_client.dart
│       └── api_response.dart
├── features/
│   ├── authentication/
│   │   ├── data/
│   │   │   ├── models/
│   │   │   ├── repositories/
│   │   │   └── datasources/
│   │   ├── domain/
│   │   │   ├── entities/
│   │   │   ├── repositories/
│   │   │   └── usecases/
│   │   └── presentation/
│   │       ├── pages/
│   │       ├── widgets/
│   │       └── providers/
│   ├── hostels/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── payments/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   ├── profile/
│   ├── search/
│   ├── favorites/
│   ├── reviews/
│   ├── maps/
│   └── campus_reps/
├── shared/
│   ├── widgets/
│   │   ├── buttons/
│   │   ├── inputs/
│   │   ├── cards/
│   │   └── dialogs/
│   ├── models/
│   └── services/
│       ├── location_service.dart
│       ├── payment_service.dart
│       └── notification_service.dart
└── main.dart
```

## 🗄️ Database Schema

### Core Tables

**users** - Multi-role user system
- Students, agents, and admins
- Email/phone verification tracking
- School affiliation and referral system

**schools** - Educational institutions
- Location data for proximity calculations
- Active status management

**hostels** - Main accommodation listings
- Consolidated room type information
- Pricing with deposits and service charges
- Rating cache for performance
- Location and amenity data

**hostel_views** - Pay-to-view tracking
- Critical for revenue model
- Tracks paid vs free views per user

### Supporting Tables
- **favorites** - User bookmarks
- **hostel_reviews** - Rating and review system
- **hostel_images** - Multi-image support with ordering
- **payments** - Transaction tracking with gateway integration
- **referrals** - User acquisition program
- **campus_reps** - Campus representative system

[View Complete Schema Diagram](./docs/database-schema.md)

## 🔐 Authentication & Authorization

### User Types & Permissions

**Students**
- Browse and search hostels
- Pay to view contact information
- Leave reviews and ratings
- Save favorites
- Refer new users

**Agents/Landlords**
- Upload and manage hostel listings
- View analytics on their properties
- Respond to reviews
- Upgrade to featured listings

**Campus Representatives**
- Upload hostels for their assigned schools
- Earn commissions on successful views
- Access to campus-specific analytics

**Admins**
- Full platform management
- User verification and moderation
- Financial oversight and reporting

### Security Implementation
```sql
-- Row Level Security Examples
CREATE POLICY "Users can only see their own data" 
ON profiles FOR ALL 
USING (auth.uid() = id);

CREATE POLICY "Agents can only edit their hostels" 
ON hostels FOR UPDATE 
USING (agent_id = auth.uid());

CREATE POLICY "Users can only see paid contact info" 
ON hostels FOR SELECT 
USING (
  is_active = true AND 
  (caretaker_phone IS NULL OR 
   EXISTS (
     SELECT 1 FROM hostel_views 
     WHERE hostel_id = hostels.id 
     AND user_id = auth.uid() 
     AND is_paid = true
   ))
);
```

## 💰 Business Logic

### Pay-to-View System
1. **Free Preview**: Basic hostel info, images, amenities
2. **Paid Access**: Contact information (phone, detailed location)
3. **Pricing**: Configurable per view or subscription-based
4. **Verification**: Track payment status in `hostel_views` table

### Revenue Streams
- **Primary**: Pay-per-view contact information
- **Secondary**: Featured listing upgrades
- **Tertiary**: Campus representative commissions

### Key Business Rules
```dart
// Example business logic
class HostelViewService {
  Future<bool> canViewContact(String hostelId, String userId) async {
    final view = await supabase
        .from('hostel_views')
        .select()
        .eq('hostel_id', hostelId)
        .eq('user_id', userId)
        .eq('is_paid', true)
        .maybeSingle();
    
    return view != null;
  }
  
  Future<void> purchaseView(String hostelId, String userId) async {
    // Process payment
    final paymentSuccess = await PaymentService.processPayment();
    
    if (paymentSuccess) {
      await supabase.from('hostel_views').insert({
        'hostel_id': hostelId,
        'user_id': userId,
        'is_paid': true,
        'viewed_at': DateTime.now().toIso8601String(),
      });
    }
  }
}
```

## 🚀 Getting Started

### Prerequisites
- Flutter SDK >=3.0.0
- Dart >=3.0.0
- Supabase account and project
- Google Maps API key (for maps)
- Payment gateway credentials (Paystack/Flutterwave)

### Environment Setup
```dart
// lib/core/config/environment.dart
abstract class Environment {
  static const supabaseUrl = String.fromEnvironment('SUPABASE_URL');
  static const supabaseAnonKey = String.fromEnvironment('SUPABASE_ANON_KEY');
  static const googleMapsApiKey = String.fromEnvironment('GOOGLE_MAPS_API_KEY');
  static const paystackPublicKey = String.fromEnvironment('PAYSTACK_PUBLIC_KEY');
}
```

### Installation
```bash
# Clone repository
git clone [repository-url]
cd hostel-aggregation-platform

# Install dependencies
flutter pub get

# Run code generation (if using freezed/json_annotation)
flutter packages pub run build_runner build

# Set up environment variables
cp .env.example .env
# Edit .env with your credentials

# Run the app
flutter run
```

### Database Setup
```bash
# Apply schema to Supabase
supabase db reset

# Enable Row Level Security
supabase db push

# Set up initial data
flutter run lib/scripts/seed_database.dart
```

## 📱 Key Features Implementation

### Hostel Search & Discovery
- **Filter System**: Price range, room type, amenities, distance
- **Map Integration**: Visual location-based search
- **Smart Recommendations**: Based on school and preferences
- **Advanced Search**: Text search with keywords

### Payment Integration
```dart
class PaymentService {
  static Future<PaymentResult> initiatePayment({
    required double amount,
    required String userId,
    required String hostelId,
  }) async {
    // Integrate with Paystack/Flutterwave
    // Handle payment callbacks
    // Update hostel_views table on success
  }
}
```

### Real-time Features
- Live chat with agents (optional)
- Real-time availability updates
- Push notifications for new listings
- Payment confirmation notifications

## 🧪 Testing Strategy

### Unit Tests
- Business logic validation
- Data model serialization
- Utility functions

### Integration Tests
- Supabase data operations
- Payment flow testing
- Authentication workflows

### Widget Tests
- UI component behavior
- Form validation
- Navigation flows

### E2E Tests
- Complete user journeys
- Payment processing
- Multi-user scenarios

## 📊 Analytics & Monitoring

### Key Metrics to Track
- **User Engagement**: Search queries, time spent, favorites
- **Conversion**: View-to-payment ratio, payment success rate
- **Content**: Popular hostels, successful listings
- **Revenue**: Daily/monthly earnings, payment method preferences

### Implementation
```dart
class AnalyticsService {
  static Future<void> trackHostelView(String hostelId, bool isPaid) async {
    await supabase.from('analytics_events').insert({
      'event_type': 'hostel_view',
      'hostel_id': hostelId,
      'is_paid': isPaid,
      'user_id': AuthService.currentUserId,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }
}
```

## 🚀 Deployment

### Flutter Build
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

### Supabase Deployment
- Database migrations via Supabase CLI
- Edge Functions for server-side logic
- Storage buckets for image uploads
- Row Level Security policies

### CI/CD Pipeline
- Automated testing on PR
- Build and deployment automation
- Database migration validation
- Environment-specific configurations

## 🔒 Security Considerations

### Data Protection
- Row Level Security on all tables
- API key rotation strategy
- Encrypted sensitive data storage
- GDPR compliance for user data

### Payment Security
- PCI DSS compliance via payment providers
- No card data storage locally
- Transaction logging and monitoring
- Fraud detection integration

## 📈 Future Enhancements

### Phase 2 Features
- **Advanced Booking**: Room reservation system
- **Virtual Tours**: 360° property viewing
- **AI Recommendations**: Machine learning-based suggestions
- **Social Features**: Student community and groups

### Phase 3 Features
- **Multi-language Support**: Localization
- **Offline Mode**: Cached listings for poor connectivity
- **Agent Analytics Dashboard**: Detailed performance metrics
- **Integration APIs**: Third-party property management systems

## 🤝 Contributing

### Code Standards
- Follow Dart style guidelines
- Implement comprehensive error handling
- Write meaningful commit messages
- Maintain test coverage >80%

### Pull Request Process
1. Feature branch from `develop`
2. Implement feature with tests
3. Update documentation
4. Submit PR with description
5. Code review and approval
6. Merge to `develop`

## 📞 Support & Contact

### Development Team
- **Project Lead**: [Name]
- **Backend**: [Name]
- **Mobile**: [Name]
- **UI/UX**: [Name]

### Documentation
- [API Documentation](./docs/api.md)
- [Database Schema](./docs/database.md)
- [Deployment Guide](./docs/deployment.md)
- [Troubleshooting](./docs/troubleshooting.md)

---

*Built with ❤️ for Nigerian students seeking quality accommodation*