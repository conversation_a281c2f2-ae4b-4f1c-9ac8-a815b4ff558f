import 'dart:io';

import '../../../core/theme/colors.dart';
import 'package:flutter/material.dart';

class ImageCard extends StatelessWidget {
  const ImageCard(
      {Key? key, required this.imageFile, required this.removeCallback})
      : super(key: key);

  final File imageFile;
  final Function() removeCallback;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Container(
      height: size.height * 0.3,
      width: size.width * 0.45,
      // color: Colors.red,
      child: Column(
        children: [
          Center(
            child: Container(
              height: size.height * 0.22,
              width: size.width * 0.38,
              decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(width: 1)),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: Image.file(
                  imageFile,
                  fit: BoxFit.fill,
                ),
              ),
            ),
          ),
          <PERSON><PERSON>(
            alignment: Alignment.bottomCenter,
            child: ElevatedButton(
              onPressed: removeCallback,
              child: Text("Remove"),
              style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor),
            ),
          )
        ],
      ),
    );
  }
}
