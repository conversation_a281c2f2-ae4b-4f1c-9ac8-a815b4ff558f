import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/api_service.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/data/models/user.dart';
import 'package:campus_find/ui/widgets/error_widget.dart';
import 'package:campus_find/ui/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:states_rebuilder/states_rebuilder.dart';
import 'package:url_launcher/url_launcher.dart';

// Create a simple way to get user ID from route arguments
String? getUserIdFromRoute() {
  // For now, return the current user ID as fallback
  // In a real app, you might want to pass this as a parameter
  return user.state?.id;
}

final userFuture = RM.injectFuture(() {
  final userId = getUserIdFromRoute() ?? user.state?.id;
  if (userId == null) {
    throw Exception('User ID not available');
  }
  return apiService.state.getUser(userId);
});

class UserPage extends StatelessWidget {
  UserPage({Key? key}) : super(key: key);

  final String _emailUrl =
      'mailto:${userFuture.state?.email}?subject=About%20Your%20Roomate%20Interest';

  final whatsAppUrl = "https://wa.me/${userFuture.state?.phone}";

  void _launchURL(String url) async => await canLaunchUrl(Uri.parse(url))
      ? await launchUrl(Uri.parse(url))
      : throw 'Could not launch $url';

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        body: SizedBox.expand(
          child: OnBuilder(
              listenTo: userFuture,
              builder: () {
                if (userFuture.isWaiting) {
                  return LoadingWidget();
                }
                if (userFuture.hasError) {
                  return AppErrorWidget(error: userFuture.error.toString());
                }
                final data = userFuture.state;
                return Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: size.height * 0.3 + 120,
                          ),
                          Card(
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20)),
                            child: Padding(
                              padding: EdgeInsets.all(10),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Text(
                                  //   "Details",
                                  //   style: TextStyle(fontSize: 22),
                                  // ),
                                  // Gap(20),
                                  ListTile(
                                    title: Text("Name:"),
                                    trailing: Text(data?.name ?? ""),
                                  ),
                                  ListTile(
                                    title: Text("School:"),
                                    trailing: Text(data?.school ?? ""),
                                  ),
                                  Divider(
                                    thickness: 2,
                                  ),
                                  ListTile(
                                      onTap: () => _launchURL(_emailUrl),
                                      leading: Icon(Icons.email),
                                      title: Text("Email"),
                                      subtitle: Text(data?.email ?? ""),
                                      trailing: Icon(Icons.arrow_forward_ios)),
                                  ListTile(
                                    onTap: () => _launchURL(whatsAppUrl),
                                    leading: Icon(Icons.mobile_friendly),
                                    title: Text("Mobile"),
                                    subtitle: Text(data?.phone ?? ""),
                                    trailing: Icon(Icons.arrow_forward_ios),
                                  ),
                                ],
                              ),
                            ),
                          )
                          // Container(
                          //   height: size.height * 0.4,
                          //   width: size.width,
                          //   decoration: BoxDecoration(
                          //     borderRadius: BorderRadius.circular(20),
                          //     color: AppColors.secondaryColor
                          //  ),
                          //   child: Text(""),
                          // )
                        ],
                      ),
                    ),
                    Positioned(
                      top: -10,
                      left: -50,
                      child: Container(
                        height: size.height * 0.3,
                        width: size.width + 100,
                        decoration: BoxDecoration(
                          color: AppColors.primaryColor,
                          borderRadius: BorderRadius.vertical(
                            bottom: Radius.circular(360),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: size.height * 0.3 - 110,
                      left: size.width / 2 - 100,
                      child: CircleAvatar(
                        radius: 100,
                        backgroundColor: Colors.white,
                        // TODO: put user image here
                        backgroundImage: NetworkImage(data?.photoUrl ?? ""),
                      ),
                    )
                  ],
                );
              }),
        ),
      ),
    );
  }
}
