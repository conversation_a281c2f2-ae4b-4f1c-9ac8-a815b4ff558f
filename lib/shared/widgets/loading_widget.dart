import 'package:flutter/material.dart';

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({Key? key, this.text}) : super(key: key);

  final String? text;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CircularProgressIndicator(),
        SizedBox(
          width: 20,
        ),
        text == null
            ? SizedBox.shrink()
            : Text(
                text!,
                style: TextStyle(fontSize: 20),
              )
      ],
    );
  }
}
