import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/data/models/notification.dart';
import 'package:campus_find/data/navigation/routes.dart';
import 'package:campus_find/data/repositories/firestore_respository.dart';
import 'package:flutter/material.dart' hide Notification;
import 'package:states_rebuilder/states_rebuilder.dart';

final notificationStream = RM.injectStream<List<Notification>>(
  () => FirestoreRepository().getNotifications(user.state!.id!),
  initialState: [],
  // onError: (_, e) => print("error"),
  debugPrintWhenNotifiedPreMessage: "Notif",
);

class NotificationsPage extends StatelessWidget {
  NotificationsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // final List notificationList = const [
    //   RequestNotification(
    //       id: "id", message: "", interestID: "", interestName: "Tester"),
    //   RequestNotification(
    //       id: "id",
    //       message: "",
    //       interestID: "",
    //       interestName: "Tester2",
    //       read: true),
    //   RequestNotification(
    //       id: "id", message: "", interestID: "", interestName: "Tester3"),
    // ];
    return Scaffold(
      appBar: AppBar(
        // automaticallyImplyLeading: false,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.black,
          onPressed: () => RM.navigate.back(),
        ),
        title: Text(
          "Notifications",
          style: TextStyle(color: Colors.black, fontSize: 22),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SafeArea(
        child: SizedBox.expand(
          child: Padding(
            padding: const EdgeInsets.only(left: 30, right: 30, bottom: 10),
            child: OnBuilder(
              listenTo: notificationStream,
              builder: () {
                if (notificationStream.isWaiting) {
                  return Center(child: CircularProgressIndicator());
                }
                if (notificationStream.hasError) {
                  return Center(child: Text('Error loading notifications'));
                }
                return ListView(shrinkWrap: true, children: [
                  SizedBox(
                    height: 30,
                  ),
                  ...notificationStream.state
                      .map((e) => e.map(
                          request: (request) =>
                              _RequestNotificationCard(notification: request),
                          lodge: (lodge) => SizedBox()))
                      .toList(),
                ]);
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _RequestNotificationCard extends StatelessWidget {
  const _RequestNotificationCard({Key? key, required this.notification})
      : super(key: key);

  final RequestNotification notification;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => RM.navigate
          .toNamed(Routes.USER_PAGE, arguments: notification.interestID),
      child: _NotificationCard(
        read: notification.read,
        title: "New interest in your roommate request",
        body: " is interested in your roommate request",
        // bolded: notification.interestName,
        buttonText: "View profile",
      ),
    );
  }
}

class _LodgeNotification extends StatelessWidget {
  const _LodgeNotification({Key? key, required this.notification})
      : super(key: key);
  final LodgeNotification notification;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {}, //TODO: Implement Lodge routing logic
      child: _NotificationCard(
        read: notification.read,
        title: "Lodge Request",
        body: notification.message,
        buttonText: "View Lodge details",
      ),
    );
  }
}

class _NotificationCard extends StatelessWidget {
  const _NotificationCard(
      {Key? key,
      required this.read,
      required this.title,
      required this.body,
      this.bolded,
      required this.buttonText})
      : super(key: key);

  final bool read;
  final String title;
  final String body;
  final String? bolded;
  final String buttonText;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 20),
      height: MediaQuery.of(context).size.height * 0.13,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              spreadRadius: 1,
              blurRadius: 10,
              color: AppColors.secondaryColor,
              offset: Offset(0, 5),
            )
          ]),
      child: Stack(
        children: [
          Row(
            children: [
              Container(
                height: MediaQuery.of(context).size.height * 0.13,
                width: 10,
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.horizontal(left: Radius.circular(20)),
                  color: AppColors.secondaryColor,
                ),
              ),
              Expanded(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          color: read ? Colors.grey : Colors.black,
                          fontWeight: read ? FontWeight.w100 : FontWeight.bold,
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Divider(
                        endIndent: 50,
                        color: Colors.grey,
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      RichText(
                        text: TextSpan(
                          text: bolded ?? "",
                          style: TextStyle(
                              color: Colors.black87,
                              fontSize: 15,
                              fontStyle: FontStyle.italic),
                          children: [
                            TextSpan(
                              text: body,
                              style: TextStyle(color: Colors.grey),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
          Align(
            alignment: Alignment.bottomRight,
            child: Container(
              height: 40,
              width: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
                color: AppColors.secondaryColor,
              ),
              child: Center(
                child: Text(
                  buttonText,
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
