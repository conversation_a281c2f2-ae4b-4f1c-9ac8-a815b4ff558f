import 'package:campus_find/constants/colors.dart';
import 'package:flutter/material.dart';

class CustomChip extends StatelessWidget {
  const CustomChip(
      {Key? key,
      required this.isSelected,
      required this.label,
      required this.callback})
      : super(key: key);

  final bool isSelected;
  final String label;
  final Function() callback;

  @override
  Widget build(BuildContext context) {
    return InputChip(
      padding: EdgeInsets.all(10),
      label: Text(label),
      labelStyle: TextStyle(
        fontSize: 18,
        color: isSelected ? Colors.white : Colors.black,
      ),
      backgroundColor: AppColors.secondaryColor,
      selectedColor: AppColors.primaryColor,
      checkmarkColor: AppColors.secondaryColor,
      selected: isSelected,
      onPressed: callback,
    );
  }
}