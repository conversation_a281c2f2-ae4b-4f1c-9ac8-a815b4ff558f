import 'package:flutter/material.dart';

class CustomDropDown extends StatelessWidget {
  const CustomDropDown(
      {Key? key,
      required this.items,
      required this.title,
      this.value,
      this.callback,
      required this.errorString})
      : super(key: key);

  final List<String> items;
  final String title;
  final String? value;
  final String errorString;
  final void Function(String?)? callback;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Card(
          elevation: 2,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Padding(
            padding: const EdgeInsets.all(15),
            child: DropdownButton<String>(
              hint: Text(title),
              value: value,
              icon: Icon(
                Icons.unfold_more,
                color: Colors.black38,
              ),
              iconSize: 24,
              elevation: 12,
              isExpanded: true,
              style: const TextStyle(color: Colors.black, fontSize: 16),
              underline: SizedBox.shrink(),
              onChanged: callback,
              items: items.map<DropdownMenuItem<String>>(
                (String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                },
              ).toList(),
            ),
          ),
        ),
        // errorString.isEmpty ? SizedBox.shrink() :
        Padding(
          padding: const EdgeInsets.only(left: 10),
          child: Text(
            errorString,
            style: TextStyle(color: Colors.red),
          ),
        )
      ],
    );
  }
}