import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:flutter/material.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        shadowColor: Colors.transparent,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back),
          color: AppColors.primaryColor,
        ),
      ),
      backgroundColor: Colors.white,
      body: SizedBox.expand(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Column(
            children: [
              CircleAvatar(
                backgroundImage: NetworkImage(user.state!.photoUrl!),
                radius: 80,
              ),
              SizedBox(
                height: 50,
              ),
              ListTile(
                title: Text("Account information"),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              Divider(),
              ListTile(
                title: Text("Change Password"),
                trailing: Icon(Icons.arrow_forward_ios),
              ),
              // Divider(),
              // ListTile(
              //   onTap: (),
              //   title: Text("About"),
              //   trailing: Icon(Icons.arrow_forward_ios),
              // ),
              // Divider(),
              // ListTile(
              //   title: Text("Contact us"),
              //   trailing: Icon(Icons.arrow_forward_ios),
              // ),
              Divider(),
              SizedBox(
                height: 100,
              ),
              CustomButtonWidget(
                  onPressed: () => user.auth.signOut(), text: "LOG OUT")
            ],
          ),
        ),
      ),
    );
  }
}
