import 'package:campus_find/constants/colors.dart';
import 'package:flutter/material.dart';

class IncrementableField extends StatelessWidget {
  const IncrementableField(
      {Key? key,
      required this.title,
      required this.value,
      required this.incrementCallback,
      required this.decrementCallback})
      : super(key: key);

  final String title;
  final String value;
  final Function() incrementCallback;
  final Function() decrementCallback;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
              child: Text(
            title,
            style: TextStyle(
              fontSize: 19,
            ),
          )),
          Row(
            children: [
              ElevatedButton(
                onPressed: decrementCallback,
                child: Icon(Icons.remove, color: Colors.black),
                style: ElevatedButton.styleFrom(
                  alignment: Alignment.center,
                  backgroundColor: AppColors.secondaryColor,
                  elevation: 0,
                  minimumSize: Size(40, 55),
                ),
              ),
              SizedBox(
                width: 60,
                child: Center(
                  child: Text(
                    value,
                    style: TextStyle(fontSize: 20),
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: incrementCallback,
                child: Center(child: Icon(Icons.add)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  minimumSize: Size(40, 55),
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
