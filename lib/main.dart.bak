import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

import 'core/config/pages.dart';

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  // await Firebase.initializeApp();

  print("Handling a background message: ${message.messageId}");
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarIconBrightness: Brightness.dark,
    statusBarColor: Colors.transparent,
  ));
  // AwesomeNotifications().initialize(null, [
  //   NotificationChannel(
  //     playSound: true,
  //     importance: NotificationImportance.Max,
  //     channelKey: 'basic_channel',
  //     channelName: 'Basic notifications',
  //     channelDescription: 'Notification channel for basic tests',
  //     defaultColor: Color(0xFF9D50DD),
  //     ledColor: Colors.white,
  //   )
  // ]);
  // AwesomeNotifications().actionStream.listen((receivedNotification) {
  //   print(receivedNotification.toString());
  //   RM.navigate.toNamed(Routes.NOTIFICATIONS, arguments: receivedNotification);
  // });
  await Firebase.initializeApp();
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
      if (!isAllowed) {
        // Insert here your friendly dialog box before call the request method
        // This is very important to not harm the user experience
        AwesomeNotifications().requestPermissionToSendNotifications();
      }
    });

    return TopAppWidget(
      ensureInitialization: () => [
        // AwesomeNotifications().initialize(null, [
        //   NotificationChannel(
        //     playSound: true,
        //     importance: NotificationImportance.Max,
        //     channelKey: 'basic_channel',
        //     channelName: 'Basic notifications',
        //     channelDescription: 'Notification channel for basic tests',
        //     defaultColor: Color(0xFF9D50DD),
        //     ledColor: Colors.white,
        //   )
        // ]),
        // Firebase.initializeApp(),
      ],
      onWaiting: () =>
          SizedBox.expand(child: Center(child: Text("Loading..."))),
      builder: (_) => MaterialApp(
        builder: (context, widget) => ResponsiveWrapper.builder(
          widget,
          maxWidth: 1200,
          minWidth: 480,
          defaultScale: true,
          breakpoints: [
            ResponsiveBreakpoint.resize(480, name: MOBILE),
            ResponsiveBreakpoint.autoScale(800, name: TABLET),
            ResponsiveBreakpoint.resize(1000, name: DESKTOP),
          ],
          background: Container(
            color: Color(0xFFF5F5F5),
          ),
        ),
        title: 'Campus Find',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          fontFamily: "SourceSansPro",
        ),
        navigatorKey: RM.navigate.navigatorKey,
        onGenerateRoute: RM.navigate.onGenerateRoute(Pages.pages),
      ),
    );
  }
}
