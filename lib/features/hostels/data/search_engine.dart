import 'dart:math' as math;
import '../../../core/models/accommodation_model.dart';
import '../../../core/models/search_criteria.dart';
import 'accommodation_repository.dart';

/// SearchEngine service that provides advanced search and filtering capabilities
/// for accommodations with location-based queries, price filtering, and sorting
class SearchEngine {
  final AccommodationRepository _repository;

  SearchEngine({AccommodationRepository? repository})
      : _repository = repository ?? AccommodationRepository();

  /// Perform a comprehensive search with all filters and sorting applied
  Future<SearchResult> search(SearchCriteria criteria) async {
    try {
      // First, get results from repository with basic filters
      final repositoryResult = await _repository.search(criteria);
      
      // Apply additional in-memory filtering and processing
      var accommodations = repositoryResult.accommodations;
      
      // Apply location-based filtering if needed
      if (criteria.location != null && criteria.maxDistanceFromCampus != null) {
        accommodations = await _filterByLocationProximity(
          accommodations,
          criteria.location!,
          criteria.maxDistanceFromCampus!,
        );
      }
      
      // Apply advanced sorting if needed
      accommodations = _applySorting(accommodations, criteria);
      
      // Calculate relevance scores for better ranking
      if (criteria.sortBy == SortOption.relevance) {
        accommodations = _applyRelevanceScoring(accommodations, criteria);
      }
      
      return SearchResult(
        accommodations: accommodations,
        totalCount: repositoryResult.totalCount,
        hasMore: repositoryResult.hasMore,
        criteria: criteria,
      );
    } catch (e) {
      throw SearchEngineException('Search failed: $e');
    }
  }

  /// Search accommodations near a specific location with enhanced filtering
  Future<List<AccommodationModel>> searchNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInKm,
    SearchCriteria? additionalCriteria,
    int limit = 20,
  }) async {
    try {
      // Get accommodations near the location
      var accommodations = await _repository.getNearLocation(
        latitude: latitude,
        longitude: longitude,
        radiusInKm: radiusInKm,
        limit: limit * 3, // Get more to allow for additional filtering
      );
      
      // Calculate distances and update accommodations
      final accommodationsWithDistance = <AccommodationModel>[];
      
      for (var accommodation in accommodations) {
        final distance = calculateDistance(
          latitude,
          longitude,
          accommodation.location.latitude,
          accommodation.location.longitude,
        );
        
        // Only include accommodations within the specified radius
        if (distance <= radiusInKm) {
          // Create new accommodation with updated distance
          final updatedAccommodation = AccommodationModel(
            id: accommodation.id,
            title: accommodation.title,
            description: accommodation.description,
            type: accommodation.type,
            location: LocationInfo(
              latitude: accommodation.location.latitude,
              longitude: accommodation.location.longitude,
              address: accommodation.location.address,
              city: accommodation.location.city,
              state: accommodation.location.state,
              country: accommodation.location.country,
              nearestCampus: accommodation.location.nearestCampus,
              distanceFromCampus: distance,
            ),
            pricing: accommodation.pricing,
            amenities: accommodation.amenities,
            imageUrls: accommodation.imageUrls,
            contact: accommodation.contact,
            rating: accommodation.rating,
            reviewCount: accommodation.reviewCount,
            isVerified: accommodation.isVerified,
            isAvailable: accommodation.isAvailable,
            createdAt: accommodation.createdAt,
            updatedAt: accommodation.updatedAt,
            ownerId: accommodation.ownerId,
            metadata: accommodation.metadata,
          );
          
          accommodationsWithDistance.add(updatedAccommodation);
        }
      }
      
      // Apply additional criteria if provided
      var filteredAccommodations = accommodationsWithDistance;
      if (additionalCriteria != null) {
        filteredAccommodations = _applyInMemoryFilters(filteredAccommodations, additionalCriteria);
        filteredAccommodations = _applySorting(filteredAccommodations, additionalCriteria);
      } else {
        // Default sort by distance if no other criteria provided
        filteredAccommodations.sort((a, b) => 
          a.location.distanceFromCampus.compareTo(b.location.distanceFromCampus));
      }
      
      return filteredAccommodations.take(limit).toList();
    } catch (e) {
      throw SearchEngineException('Location-based search failed: $e');
    }
  }

  /// Get search suggestions based on partial query
  Future<List<String>> getSearchSuggestions(String query) async {
    try {
      if (query.length < 2) return [];
      
      final suggestions = <String>{};
      final queryLower = query.toLowerCase();
      
      // Get a sample of accommodations to generate suggestions
      final accommodations = await _repository.getAll(limit: 100);
      
      for (final accommodation in accommodations) {
        // Add title suggestions
        if (accommodation.title.toLowerCase().contains(queryLower)) {
          suggestions.add(accommodation.title);
        }
        
        // Add location suggestions
        if (accommodation.location.address.toLowerCase().contains(queryLower)) {
          suggestions.add(accommodation.location.address);
        }
        
        if (accommodation.location.city != null &&
            accommodation.location.city!.toLowerCase().contains(queryLower)) {
          suggestions.add(accommodation.location.city!);
        }
        
        // Add amenity suggestions
        for (final amenity in accommodation.amenities) {
          if (amenity.toLowerCase().contains(queryLower)) {
            suggestions.add(amenity);
          }
        }
      }
      
      return suggestions.take(10).toList()..sort();
    } catch (e) {
      throw SearchEngineException('Failed to get search suggestions: $e');
    }
  }

  /// Filter accommodations by price range
  List<AccommodationModel> filterByPrice({
    required List<AccommodationModel> accommodations,
    double? minPrice,
    double? maxPrice,
  }) {
    return accommodations.where((accommodation) {
      final price = accommodation.pricing.monthly;
      
      if (minPrice != null && price < minPrice) return false;
      if (maxPrice != null && price > maxPrice) return false;
      
      return true;
    }).toList();
  }

  /// Filter accommodations by amenities
  List<AccommodationModel> filterByAmenities({
    required List<AccommodationModel> accommodations,
    required List<String> requiredAmenities,
  }) {
    if (requiredAmenities.isEmpty) return accommodations;
    
    return accommodations.where((accommodation) {
      return requiredAmenities.every(
        (amenity) => accommodation.amenities.contains(amenity),
      );
    }).toList();
  }

  /// Filter accommodations by type
  List<AccommodationModel> filterByType({
    required List<AccommodationModel> accommodations,
    required AccommodationType type,
  }) {
    if (type == AccommodationType.any) return accommodations;
    
    return accommodations.where((accommodation) {
      return accommodation.type == type;
    }).toList();
  }

  /// Sort accommodations based on criteria
  List<AccommodationModel> sortAccommodations({
    required List<AccommodationModel> accommodations,
    required SortOption sortBy,
    required SortOrder sortOrder,
  }) {
    final sortedList = List<AccommodationModel>.from(accommodations);
    
    sortedList.sort((a, b) {
      int comparison;
      
      switch (sortBy) {
        case SortOption.price:
          comparison = a.pricing.monthly.compareTo(b.pricing.monthly);
          break;
        case SortOption.rating:
          comparison = a.rating.compareTo(b.rating);
          break;
        case SortOption.distance:
          comparison = a.location.distanceFromCampus.compareTo(b.location.distanceFromCampus);
          break;
        case SortOption.newest:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case SortOption.oldest:
          comparison = b.createdAt.compareTo(a.createdAt);
          break;
        case SortOption.relevance:
          // For relevance, we'll use a combination of rating and verification
          final aScore = _calculateRelevanceScore(a);
          final bScore = _calculateRelevanceScore(b);
          comparison = aScore.compareTo(bScore);
          break;
      }
      
      return sortOrder == SortOrder.ascending ? comparison : -comparison;
    });
    
    return sortedList;
  }

  /// Calculate distance between two geographical points using Haversine formula
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = 
        math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) * 
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    final double c = 2 * math.asin(math.sqrt(a));
    
    return earthRadius * c;
  }

  /// Get available filter options from current data
  Future<FilterOptions> getFilterOptions() async {
    try {
      return await _repository.getFilterOptions();
    } catch (e) {
      throw SearchEngineException('Failed to get filter options: $e');
    }
  }

  /// Advanced search with multiple location criteria
  Future<SearchResult> searchByMultipleLocations({
    required List<String> locations,
    SearchCriteria? baseCriteria,
  }) async {
    try {
      final allResults = <AccommodationModel>[];
      final criteria = baseCriteria ?? const SearchCriteria();
      
      for (final location in locations) {
        final locationCriteria = criteria.copyWith(location: location);
        final result = await search(locationCriteria);
        allResults.addAll(result.accommodations);
      }
      
      // Remove duplicates based on accommodation ID
      final uniqueResults = <String, AccommodationModel>{};
      for (final accommodation in allResults) {
        uniqueResults[accommodation.id] = accommodation;
      }
      
      var finalResults = uniqueResults.values.toList();
      
      // Apply sorting to combined results
      finalResults = _applySorting(finalResults, criteria);
      
      return SearchResult(
        accommodations: finalResults.take(criteria.limit).toList(),
        totalCount: finalResults.length,
        hasMore: finalResults.length > criteria.limit,
        criteria: criteria,
      );
    } catch (e) {
      throw SearchEngineException('Multi-location search failed: $e');
    }
  }

  /// Filter accommodations by rating range
  List<AccommodationModel> filterByRating({
    required List<AccommodationModel> accommodations,
    double? minRating,
    double? maxRating,
  }) {
    return accommodations.where((accommodation) {
      final rating = accommodation.rating;
      
      if (minRating != null && rating < minRating) return false;
      if (maxRating != null && rating > maxRating) return false;
      
      return true;
    }).toList();
  }

  /// Filter accommodations by availability status
  List<AccommodationModel> filterByAvailability({
    required List<AccommodationModel> accommodations,
    required bool isAvailable,
  }) {
    return accommodations.where((accommodation) {
      return accommodation.isAvailable == isAvailable;
    }).toList();
  }

  /// Filter accommodations by verification status
  List<AccommodationModel> filterByVerification({
    required List<AccommodationModel> accommodations,
    required bool isVerified,
  }) {
    return accommodations.where((accommodation) {
      return accommodation.isVerified == isVerified;
    }).toList();
  }

  /// Advanced filtering with multiple criteria
  List<AccommodationModel> applyAdvancedFilters({
    required List<AccommodationModel> accommodations,
    double? minPrice,
    double? maxPrice,
    double? minRating,
    double? maxRating,
    List<String>? requiredAmenities,
    AccommodationType? type,
    bool? isAvailable,
    bool? isVerified,
    double? maxDistanceFromCampus,
  }) {
    var filtered = accommodations;
    
    // Apply price filter
    if (minPrice != null || maxPrice != null) {
      filtered = filterByPrice(
        accommodations: filtered,
        minPrice: minPrice,
        maxPrice: maxPrice,
      );
    }
    
    // Apply rating filter
    if (minRating != null || maxRating != null) {
      filtered = filterByRating(
        accommodations: filtered,
        minRating: minRating,
        maxRating: maxRating,
      );
    }
    
    // Apply amenities filter
    if (requiredAmenities != null && requiredAmenities.isNotEmpty) {
      filtered = filterByAmenities(
        accommodations: filtered,
        requiredAmenities: requiredAmenities,
      );
    }
    
    // Apply type filter
    if (type != null && type != AccommodationType.any) {
      filtered = filterByType(
        accommodations: filtered,
        type: type,
      );
    }
    
    // Apply availability filter
    if (isAvailable != null) {
      filtered = filterByAvailability(
        accommodations: filtered,
        isAvailable: isAvailable,
      );
    }
    
    // Apply verification filter
    if (isVerified != null) {
      filtered = filterByVerification(
        accommodations: filtered,
        isVerified: isVerified,
      );
    }
    
    // Apply distance filter
    if (maxDistanceFromCampus != null) {
      filtered = filtered.where((accommodation) {
        return accommodation.location.distanceFromCampus <= maxDistanceFromCampus;
      }).toList();
    }
    
    return filtered;
  }

  /// Search accommodations within a specific campus area
  Future<List<AccommodationModel>> searchByCampus({
    required String campusName,
    double maxDistanceKm = 5.0,
    SearchCriteria? additionalCriteria,
    int limit = 20,
  }) async {
    try {
      // First, search by campus name in location fields
      final campusCriteria = SearchCriteria(
        query: campusName,
        maxDistanceFromCampus: maxDistanceKm,
        limit: limit * 2,
      );
      
      final result = await search(campusCriteria);
      var accommodations = result.accommodations;
      
      // Apply additional filtering if provided
      if (additionalCriteria != null) {
        accommodations = _applyInMemoryFilters(accommodations, additionalCriteria);
        accommodations = _applySorting(accommodations, additionalCriteria);
      }
      
      return accommodations.take(limit).toList();
    } catch (e) {
      throw SearchEngineException('Campus search failed: $e');
    }
  }

  // Private helper methods

  /// Apply location-based proximity filtering
  Future<List<AccommodationModel>> _filterByLocationProximity(
    List<AccommodationModel> accommodations,
    String location,
    double maxDistance,
  ) async {
    // This is a simplified implementation
    // In production, you'd want to geocode the location string to coordinates
    return accommodations.where((accommodation) {
      return accommodation.location.distanceFromCampus <= maxDistance ||
             accommodation.location.city?.toLowerCase() == location.toLowerCase() ||
             accommodation.location.address.toLowerCase().contains(location.toLowerCase());
    }).toList();
  }

  /// Apply in-memory filters that couldn't be applied at database level
  List<AccommodationModel> _applyInMemoryFilters(
    List<AccommodationModel> accommodations,
    SearchCriteria criteria,
  ) {
    var filtered = accommodations;
    
    // Filter by text query
    if (criteria.query != null && criteria.query!.isNotEmpty) {
      final query = criteria.query!.toLowerCase();
      filtered = filtered.where((accommodation) {
        return accommodation.title.toLowerCase().contains(query) ||
               accommodation.description.toLowerCase().contains(query) ||
               accommodation.location.address.toLowerCase().contains(query) ||
               accommodation.amenities.any((amenity) => 
                   amenity.toLowerCase().contains(query));
      }).toList();
    }
    
    // Filter by amenities
    if (criteria.amenities.isNotEmpty) {
      filtered = filterByAmenities(
        accommodations: filtered,
        requiredAmenities: criteria.amenities,
      );
    }
    
    // Filter by price range
    filtered = filterByPrice(
      accommodations: filtered,
      minPrice: criteria.minPrice,
      maxPrice: criteria.maxPrice,
    );
    
    // Filter by type
    if (criteria.type != null) {
      filtered = filterByType(
        accommodations: filtered,
        type: criteria.type!,
      );
    }
    
    return filtered;
  }

  /// Apply sorting to accommodations
  List<AccommodationModel> _applySorting(
    List<AccommodationModel> accommodations,
    SearchCriteria criteria,
  ) {
    return sortAccommodations(
      accommodations: accommodations,
      sortBy: criteria.sortBy,
      sortOrder: criteria.sortOrder,
    );
  }

  /// Apply relevance scoring for better search results
  List<AccommodationModel> _applyRelevanceScoring(
    List<AccommodationModel> accommodations,
    SearchCriteria criteria,
  ) {
    final scoredAccommodations = accommodations.map((accommodation) {
      final score = _calculateRelevanceScore(accommodation, criteria);
      return _ScoredAccommodation(accommodation, score);
    }).toList();
    
    scoredAccommodations.sort((a, b) => b.score.compareTo(a.score));
    
    return scoredAccommodations.map((scored) => scored.accommodation).toList();
  }

  /// Calculate relevance score for an accommodation
  double _calculateRelevanceScore(AccommodationModel accommodation, [SearchCriteria? criteria]) {
    double score = 0.0;
    
    // Base score from rating and reviews
    score += accommodation.rating * 2;
    score += math.log(accommodation.reviewCount + 1) * 0.5;
    
    // Verification bonus
    if (accommodation.isVerified) {
      score += 1.0;
    }
    
    // Availability bonus
    if (accommodation.isAvailable) {
      score += 0.5;
    }
    
    // Query matching bonus
    if (criteria?.query != null && criteria!.query!.isNotEmpty) {
      final query = criteria.query!.toLowerCase();
      if (accommodation.title.toLowerCase().contains(query)) {
        score += 2.0;
      }
      if (accommodation.description.toLowerCase().contains(query)) {
        score += 1.0;
      }
    }
    
    // Recency bonus (newer accommodations get slight boost)
    final daysSinceCreation = DateTime.now().difference(accommodation.createdAt).inDays;
    if (daysSinceCreation < 30) {
      score += 0.3;
    }
    
    return score;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}

/// Helper class for relevance scoring
class _ScoredAccommodation {
  final AccommodationModel accommodation;
  final double score;
  
  _ScoredAccommodation(this.accommodation, this.score);
}

/// Exception thrown by SearchEngine operations
class SearchEngineException implements Exception {
  final String message;
  
  const SearchEngineException(this.message);
  
  @override
  String toString() => 'SearchEngineException: $message';
}