import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/models/accommodation_model.dart';
import '../../../core/models/search_criteria.dart';

class AccommodationRepository {
  final FirebaseFirestore _firestore;
  static const String _collection = 'accommodations';

  AccommodationRepository({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  /// Create a new accommodation
  Future<void> create(AccommodationModel accommodation) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(accommodation.id)
          .set(accommodation.toJson());
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to create accommodation: $e',
      );
    }
  }

  /// Get accommodation by ID
  Future<AccommodationModel?> getById(String id) async {
    try {
      final doc = await _firestore.collection(_collection).doc(id).get();
      
      if (!doc.exists) {
        return null;
      }

      final data = doc.data()!;
      data['id'] = doc.id;
      return AccommodationModel.fromJson(data);
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to get accommodation: $e',
      );
    }
  }

  /// Update an existing accommodation
  Future<void> update(AccommodationModel accommodation) async {
    try {
      final updateData = accommodation.toJson();
      updateData['updatedAt'] = DateTime.now().toIso8601String();
      
      await _firestore
          .collection(_collection)
          .doc(accommodation.id)
          .update(updateData);
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to update accommodation: $e',
      );
    }
  }

  /// Delete an accommodation
  Future<void> delete(String id) async {
    try {
      await _firestore.collection(_collection).doc(id).delete();
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to delete accommodation: $e',
      );
    }
  }

  /// Search accommodations based on criteria
  Future<SearchResult> search(SearchCriteria criteria) async {
    try {
      Query query = _firestore.collection(_collection);

      // Apply filters
      query = _applyFilters(query, criteria);

      // Apply sorting
      query = _applySorting(query, criteria);

      // Apply pagination
      if (criteria.offset > 0) {
        final offsetQuery = _firestore
            .collection(_collection)
            .orderBy(_getSortField(criteria.sortBy), 
                    descending: criteria.sortOrder == SortOrder.descending)
            .limit(criteria.offset);
        
        final offsetSnapshot = await offsetQuery.get();
        if (offsetSnapshot.docs.isNotEmpty) {
          query = query.startAfterDocument(offsetSnapshot.docs.last);
        }
      }

      query = query.limit(criteria.limit + 1); // +1 to check if there are more results

      final snapshot = await query.get();
      final accommodations = <AccommodationModel>[];
      
      for (int i = 0; i < snapshot.docs.length && i < criteria.limit; i++) {
        final doc = snapshot.docs[i];
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        final accommodation = AccommodationModel.fromJson(data);
        
        // Filter by amenities in memory (due to Firestore limitations)
        if (criteria.amenities.isNotEmpty) {
          final hasAllAmenities = criteria.amenities.every(
            (amenity) => accommodation.amenities.contains(amenity),
          );
          if (!hasAllAmenities) continue;
        }
        
        // Filter by text query in memory
        if (criteria.query != null && criteria.query!.isNotEmpty) {
          final query = criteria.query!.toLowerCase();
          final matchesQuery = accommodation.title.toLowerCase().contains(query) ||
              accommodation.description.toLowerCase().contains(query) ||
              accommodation.location.address.toLowerCase().contains(query) ||
              accommodation.amenities.any((amenity) => amenity.toLowerCase().contains(query));
          if (!matchesQuery) continue;
        }
        
        accommodations.add(accommodation);
      }

      final hasMore = snapshot.docs.length > criteria.limit;
      
      // Get total count for the search (this is expensive, consider caching)
      final totalCount = await _getTotalCount(criteria);

      return SearchResult(
        accommodations: accommodations,
        totalCount: totalCount,
        hasMore: hasMore,
        criteria: criteria,
      );
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to search accommodations: $e',
      );
    }
  }

  /// Get all accommodations (with pagination)
  Future<List<AccommodationModel>> getAll({
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return AccommodationModel.fromJson(data);
      }).toList();
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to get accommodations: $e',
      );
    }
  }

  /// Get accommodations by owner ID
  Future<List<AccommodationModel>> getByOwnerId(String ownerId) async {
    try {
      final snapshot = await _firestore
          .collection(_collection)
          .where('ownerId', isEqualTo: ownerId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return AccommodationModel.fromJson(data);
      }).toList();
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to get accommodations by owner: $e',
      );
    }
  }

  /// Get accommodations near a location
  Future<List<AccommodationModel>> getNearLocation({
    required double latitude,
    required double longitude,
    required double radiusInKm,
    int limit = 20,
  }) async {
    try {
      // Note: This is a simplified implementation
      // For production, consider using GeoFlutterFire or similar for proper geospatial queries
      final snapshot = await _firestore
          .collection(_collection)
          .where('isAvailable', isEqualTo: true)
          .limit(limit * 3) // Get more to filter by distance
          .get();

      final accommodations = <AccommodationModel>[];
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        data['id'] = doc.id;
        final accommodation = AccommodationModel.fromJson(data);
        
        final distance = _calculateDistance(
          latitude,
          longitude,
          accommodation.location.latitude,
          accommodation.location.longitude,
        );
        
        if (distance <= radiusInKm) {
          accommodations.add(accommodation);
        }
        
        if (accommodations.length >= limit) break;
      }

      return accommodations;
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to get accommodations near location: $e',
      );
    }
  }

  /// Get filter options for search
  Future<FilterOptions> getFilterOptions() async {
    try {
      final snapshot = await _firestore.collection(_collection).get();
      
      final amenities = <String>{};
      final types = <AccommodationType>{};
      final locations = <String>{};
      double minPrice = double.infinity;
      double maxPrice = 0;

      for (final doc in snapshot.docs) {
        final data = doc.data();
        final accommodation = AccommodationModel.fromJson({...data, 'id': doc.id});
        
        amenities.addAll(accommodation.amenities);
        types.add(accommodation.type);
        if (accommodation.location.city != null) {
          locations.add(accommodation.location.city!);
        }
        
        if (accommodation.pricing.monthly < minPrice) {
          minPrice = accommodation.pricing.monthly;
        }
        if (accommodation.pricing.monthly > maxPrice) {
          maxPrice = accommodation.pricing.monthly;
        }
      }

      return FilterOptions(
        availableAmenities: amenities.toList()..sort(),
        availableTypes: types.toList(),
        priceRange: PriceRange(
          min: minPrice == double.infinity ? 0 : minPrice,
          max: maxPrice,
        ),
        availableLocations: locations.toList()..sort(),
      );
    } catch (e) {
      throw AccommodationRepositoryException(
        'Failed to get filter options: $e',
      );
    }
  }

  /// Apply filters to the query
  Query _applyFilters(Query query, SearchCriteria criteria) {
    // Always filter by availability if specified
    if (criteria.isAvailable != null) {
      query = query.where('isAvailable', isEqualTo: criteria.isAvailable);
    }

    // Filter by verification status
    if (criteria.isVerified != null) {
      query = query.where('isVerified', isEqualTo: criteria.isVerified);
    }

    // Filter by accommodation type
    if (criteria.type != null && criteria.type != AccommodationType.any) {
      query = query.where('type', isEqualTo: criteria.type.toString().split('.').last);
    }

    // Filter by price range
    if (criteria.minPrice != null) {
      query = query.where('pricing.monthly', isGreaterThanOrEqualTo: criteria.minPrice);
    }
    if (criteria.maxPrice != null) {
      query = query.where('pricing.monthly', isLessThanOrEqualTo: criteria.maxPrice);
    }

    // Filter by location (city)
    if (criteria.location != null && criteria.location!.isNotEmpty) {
      query = query.where('location.city', isEqualTo: criteria.location);
    }

    // Note: Amenities filtering is done in memory due to Firestore limitations
    // For production, consider using array-contains-any or restructuring data

    return query;
  }

  /// Apply sorting to the query
  Query _applySorting(Query query, SearchCriteria criteria) {
    final field = _getSortField(criteria.sortBy);
    final descending = criteria.sortOrder == SortOrder.descending;
    
    return query.orderBy(field, descending: descending);
  }

  /// Get the Firestore field name for sorting
  String _getSortField(SortOption sortBy) {
    switch (sortBy) {
      case SortOption.price:
        return 'pricing.monthly';
      case SortOption.rating:
        return 'rating';
      case SortOption.distance:
        return 'location.distanceFromCampus';
      case SortOption.newest:
        return 'createdAt';
      case SortOption.oldest:
        return 'createdAt';
      case SortOption.relevance:
        return 'createdAt'; // Default to creation date for relevance
    }
  }

  /// Get total count for search results (expensive operation)
  Future<int> _getTotalCount(SearchCriteria criteria) async {
    try {
      Query query = _firestore.collection(_collection);
      query = _applyFilters(query, criteria);
      
      final snapshot = await query.get();
      return snapshot.docs.length;
    } catch (e) {
      // Return 0 if count fails to avoid breaking the search
      return 0;
    }
  }

  /// Calculate distance between two points using Haversine formula
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    
    final double a = 
        math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) * math.cos(_degreesToRadians(lat2)) * 
        math.sin(dLon / 2) * math.sin(dLon / 2);
    
    final double c = 2 * math.asin(math.sqrt(a));
    
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }
}

class AccommodationRepositoryException implements Exception {
  final String message;
  
  const AccommodationRepositoryException(this.message);
  
  @override
  String toString() => 'AccommodationRepositoryException: $message';
}