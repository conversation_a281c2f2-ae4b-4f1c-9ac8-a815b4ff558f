import 'package:badges/badges.dart' as badges;
import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/accomodationController.dart';
import 'package:campus_find/data/controllers/api_service.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/data/models/accommodation.dart';
import 'package:campus_find/data/navigation/routes.dart';
import 'package:campus_find/ui/app/common/notification_page.dart';
import 'package:campus_find/ui/widgets/error_widget.dart';
import 'package:campus_find/ui/widgets/loading_widget.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class AccommodationHome extends StatelessWidget {
  AccommodationHome({Key? key}) : super(key: key);

  final notif = RM.injectStream(() => FirebaseMessaging.onMessage,
      // ignore: top_level_function_literal_block
      onInitialized: (s, _) async {
    String? token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      await apiService.state.addTokenToDatabase(token);
    }
  });
  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent),
      child: Scaffold(
        backgroundColor: Colors.white,
        // floatingActionButton: FloatingActionButton.extended(
        //   label: Text("Create", style: TextStyle(fontSize: 18)),
        //   backgroundColor: AppColors.primaryColor,
        //   onPressed: () {
        //     showBarModalBottomSheet(
        //       context: context,
        //       builder: (context) => BottomSheetWidget(),
        //     );
        //   },
        //   icon: Icon(
        //     Icons.add,
        //     color: Colors.white,
        //   ),
        // ),
        body: SizedBox.expand(child: _Home()),
      ),
    );
  }
}

class _Home extends StatelessWidget {
  const _Home({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SizedBox.expand(
        child: OnBuilder(
          listenTo: accommodationController,
          builder: () {
            if (accommodationController.isWaiting) {
              return LoadingWidget();
            }
            if (accommodationController.hasError) {
              return AppErrorWidget(
                  error: accommodationController.error.toString());
            }
            if (accommodationController.hasData) {
              return _buildBody(context);
            }
            return AppErrorWidget(error: "waiting");
          },
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Welcome,",
                    style: TextStyle(fontSize: 24),
                  ),
                  Gap(5),
                  Text(
                    user.state?.name ?? "",
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                ],
              ),
              Spacer(),
              OnBuilder(
                listenTo: notificationStream,
                builder: () {
                  int count = notificationStream.state
                      .where((element) => !element.read)
                      .toList()
                      .length;
                  return badges.Badge(
                    position: badges.BadgePosition.topEnd(top: 0, end: 0),
                    animationType: badges.BadgeAnimationType.scale,
                    badgeContent:
                        Text('$count', style: TextStyle(color: Colors.white)),
                    child: IconButton(
                      onPressed: () =>
                          RM.navigate.toNamed(Routes.NOTIFICATIONS),
                      icon: Icon(
                        Icons.notifications,
                        color: AppColors.primaryColor,
                        size: 35,
                      ),
                    ),
                  );
                },
              ),
              SizedBox(width: 15),
              IconButton(
                // onPressed: () => RM.navigate.toNamed(Routes.DRAWER),
                onPressed: () => user.auth.signOut(),
                icon: Icon(
                  // Icons.menu,
                  Icons.exit_to_app_outlined,
                  color: AppColors.primaryColor,
                  size: 35,
                ),
              ),
              // GestureDetector(
              //   onTap: () {},
              //   child: Container(
              //     height: 50,
              //     width: 50,
              //     decoration: BoxDecoration(
              //         color: Color(0xFF36337f),
              //         borderRadius: BorderRadius.circular(10)),
              //     child: Center(
              //       child: Icon(
              //         Icons.person,
              //         color: Colors.white,
              //       ),
              //     ),
              //   ),
              // )
            ],
          ),
        ),
        Gap(10),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              Gap(20),
              CategoryCard(
                  title: "All",
                  icon: Icons.all_inclusive_outlined,
                  cardFilter: FilterParam.All),
              CategoryCard(
                  title: "Lodges",
                  icon: Icons.home_outlined,
                  cardFilter: FilterParam.Lodges),
              CategoryCard(
                  title: "Rommates",
                  icon: Icons.person_add_outlined,
                  cardFilter: FilterParam.Roommate),
              CategoryCard(
                  title: "Self-con",
                  icon: Icons.bed_outlined,
                  cardFilter: FilterParam.SelfCon),
              CategoryCard(
                  title: "One Bedroom",
                  icon: Icons.bedroom_parent_outlined,
                  cardFilter: FilterParam.TwoBedroom)
            ],
          ),
        ),
        Gap(20),
        Expanded(
          child: RefreshIndicator(
            onRefresh: () =>
                accommodationController.state.refreshAccommodationsList(),
            child: SingleChildScrollView(
              controller: scroll.controller,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    Gap(20),
                    Row(
                      children: [
                        Text(
                          "Nearby",
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      ],
                    ),
                    Gap(20),
                    for (Accommodation accommodation
                        in accommodationController.state.accommodation)
                      _buildAccomodationCard(
                          size: size, accommodation: accommodation),
                    OnBuilder(
                      listenTo: scroll,
                      builder: () {
                        if (!scroll.hasData) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: CircularProgressIndicator(),
                          );
                        }
                        return SizedBox.shrink();
                      },
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

// Card for roommate requests
  Widget _buildAccomodationCard({
    required Size size,
    required Accommodation accommodation,
  }) {
    return GestureDetector(
      onTap: () {
        accommodationController.state.currentlyViewed = accommodation;
        RM.navigate.toNamed(Routes.ROOMMATE_DETAILS);
      },
      child: Container(
        height: 160,
        width: size.width,
        margin: EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          // color: Color(0xFFecf1fd),
          color: AppColors.secondaryColor,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
                color: Colors.white30,
                offset: Offset(1, 5),
                blurRadius: 7,
                spreadRadius: 0.5)
          ],
        ),
        child: Row(
          children: [
            Flexible(
              flex: 4,
              child: ClipRRect(
                borderRadius:
                    BorderRadius.horizontal(left: Radius.circular(15)),
                child: SizedBox.expand(
                    child: Hero(
                  tag: accommodation.id!,
                  child: accommodation.images?[0] != null
                      ? Image.network(
                          accommodation.images![0],
                          fit: BoxFit.fill,
                        )
                      : Image.asset(
                          'assets/images/room.jpg',
                          fit: BoxFit.fill,
                        ),
                )),
              ),
            ),
            Flexible(
              flex: 6,
              child: Padding(
                padding: const EdgeInsets.only(
                    top: 10, right: 15, bottom: 10, left: 15),
                child: accommodation.map(
                    lodge: (lodge) => _LodgeCard(lodge: lodge),
                    roommate: (roommate) => _buildRoommateRequest(roommate)),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildRoommateRequest(Roommate roommate) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text(
            "${roommate.gender} Roommate Needed",
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          Gap(5),
          _buildRequest(
              icon: Icon(Icons.location_on, size: 22),
              value: roommate.location!.area!),
          _buildRequest(
              icon: Icon(Icons.home, size: 22), value: roommate.roomType!),
          _buildRequest(
              icon: Icon(Icons.money_off, size: 22), value: roommate.price!),
          Row(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(
                    Icons.person,
                    size: 18,
                  ),
                  Gap(2),
                  Text(roommate.numberOfRoomates!)
                ],
              ),
              Gap(10),
              Row(
                children: [
                  Icon(
                    Icons.person_add,
                    size: 18,
                  ),
                  Gap(2),
                  Text(roommate.availableSpaces.toString())
                ],
              ),
              Spacer(),
              Text(
                roommate.status!,
                style: TextStyle(
                  color: roommate.status! == "Available"
                      ? Colors.green
                      : Colors.red,
                ),
              )
            ],
          )
        ],
      ),
    );
  }

// Widget for each field in the roommate request card
  Widget _buildRequest({required Icon icon, required String value}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 7),
      child: Row(
        children: [
          // Text(
          //   "$name: ",
          //   style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          // ),
          icon,
          SizedBox(
            width: 10,
          ),
          Text(
            "$value",
            style: TextStyle(fontSize: 16),
          )
        ],
      ),
    );
  }
}

class CategoryCard extends StatelessWidget {
  const CategoryCard(
      {Key? key,
      required this.icon,
      required this.title,
      required this.cardFilter})
      : super(key: key);

  final IconData icon;
  final String title;
  final FilterParam cardFilter;

  @override
  Widget build(BuildContext context) {
    final currentfilter = accommodationController.state.filter;
    return GestureDetector(
      onTap: () => accommodationController.setState((s) {
        s.setFilterParam(cardFilter);
      }),
      child: Container(
        height: 85,
        width: 85,
        margin: EdgeInsets.only(right: 10),
        padding: EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: currentfilter == cardFilter
              ? Color(0xFF36337f)
              : Color(0xFFecf1fd),
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon,
                size: 30,
                color:
                    currentfilter == cardFilter ? Colors.white : Colors.black),
            Gap(10),
            Text(
              title,
              style: TextStyle(
                color:
                    currentfilter == cardFilter ? Colors.white : Colors.black,
                fontSize: 15,
              ),
              textAlign: TextAlign.center,
            )
          ],
        ),
      ),
    );
  }
}

class _LodgeCard extends StatelessWidget {
  const _LodgeCard({Key? key, required this.lodge}) : super(key: key);

  final Lodge lodge;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${lodge.roomType} Apartment",
            style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 20,
          ),
          Row(
            children: [
              Icon(Icons.location_on, size: 22),
              SizedBox(width: 10),
              Text(
                lodge.location?.area ?? "",
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),

          // _buildRequest(name: "Institution", value: "UNIPORT"),
          // _buildRequest(name: "Location", value: "Aluu"),
          // _buildRequest(name: "Posted", value: "2 day ago"),
          Spacer(),
          Row(
            children: [
              Icon(Icons.money_off, size: 22),
              SizedBox(width: 10),
              Text(lodge.price ?? "", style: TextStyle(fontSize: 16)),
              Spacer(),
              Text(
                lodge.status ?? "",
                style: TextStyle(color: Colors.green),
              )
            ],
          )
        ],
      ),
    );
  }
}

class BottomSheetButton extends StatelessWidget {
  const BottomSheetButton(
      {Key? key,
      required this.callback,
      required this.icon,
      required this.title})
      : super(key: key);
  final Function() callback;
  final IconData icon;
  final String title;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: callback,
      child: Container(
        height: 100,
        width: 100,
        margin: EdgeInsets.only(right: 10),
        padding: EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: AppColors.primaryColor,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 35, color: Colors.white),
            Gap(10),
            Text(
              title,
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            )
          ],
        ),
      ),
    );
  }
}

class BottomSheetWidget extends StatelessWidget {
  const BottomSheetWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height / 5,
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // BottomSheetButton(
          //   callback: () {
          //     createRoommateController.state.setAccommodationType(true);
          //     RM.navigate.toNamed(Routes.CREATE_LODGE_DETAILS);
          //   },
          //   icon: Icons.home,
          //   title: "Add Lodge",
          // ),
          // SizedBox(width: 20),
          BottomSheetButton(
            callback: () =>
                RM.navigate.toNamed(Routes.CREATE_REQUEST_ROOM_DETAILS),
            icon: Icons.person_add,
            title: "Add Request",
          ),
          // BottomSheetButton(
          //   callback: () {},
          //   icon: Icons.home,
          //   title: "Add Notification",
          // )
        ],
      ),
    );
  }
}
