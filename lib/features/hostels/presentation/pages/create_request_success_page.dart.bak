import 'package:campus_find/data/navigation/routes.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class CreateRequestSuccessPage extends StatelessWidget {
  const CreateRequestSuccessPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SizedBox.expand(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 250),
              SizedBox(
                height: 200,
                child: Image.asset(
                  "assets/images/tick.png",
                  fit: BoxFit.fill,
                ),
              ),
              Sized<PERSON>ox(height: 70),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  "Your roommate request has been added successfully. \nYou will be contacted when a user indicates interest.",
                  style: TextStyle(fontSize: 22),
                  textAlign: TextAlign.center,
                ),
              ),
              <PERSON><PERSON><PERSON><PERSON>(
                height: 70,
              ),
              CustomButtonWidget(
                onPressed: () => RM.navigate.backUntil(Routes.ACCOMMODATION),
                text: "Browse other accommodations",
              )
            ],
          ),
        ),
      ),
    );
  }
}
