import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/create_roomate_controller.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:campus_find/ui/app/common/custom_dropdown.dart';
import 'package:campus_find/ui/app/common/custom_select_chip.dart';
import 'package:campus_find/ui/app/common/incrementable_field.dart';
import 'package:flutter/material.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class CreateRequestRoommatePref extends StatelessWidget {
  const CreateRequestRoommatePref({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.black,
          onPressed: () => RM.navigate.back(),
        ),
      ),
      body: SafeArea(
        child: SizedBox.expand(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: OnReactive(
              () {
                final state = createRoommateController.state;
                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 30),
                      Text(
                        "About the roommate",
                        style: TextStyle(
                            fontSize: 30, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 30),
                      Card(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20)),
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                "Select Gender",
                                style: TextStyle(fontSize: 18),
                              ),
                              SizedBox(height: 30),
                              Row(
                                children: [
                                  CustomChip(
                                    isSelected: state.gender == "Male",
                                    label: 'Male',
                                    callback: () =>
                                        createRoommateController.setState(
                                      (s) {
                                        s.setGender("Male");
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 20),
                                  CustomChip(
                                    isSelected: state.gender == "Female",
                                    label: "Female",
                                    callback: () =>
                                        createRoommateController.setState(
                                      (s) {
                                        s.setGender("Female");
                                      },
                                    ),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 20),
                      CustomDropDown(
                        errorString: state.levelError,
                        items: state.levelList,
                        title: "Select Level",
                        value: state.level == '' ? null : state.level,
                        callback: (String? value) =>
                            createRoommateController.setState((s) {
                          s.setLevel(value!);
                        }),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      IncrementableField(
                        title: "Total Roommates",
                        value: state.numberOfRoommates.toString(),
                        incrementCallback: () =>
                            createRoommateController.setState((s) {
                          s.increaseNumberOfRoommates();
                        }),
                        decrementCallback: () =>
                            createRoommateController.setState((s) {
                          s.decrementNumberOfRoommates();
                        }),
                      ),
                      IncrementableField(
                        title: "Available Spaces",
                        value: state.availableSpaces.toString(),
                        incrementCallback: () =>
                            createRoommateController.setState((s) {
                          s.increaseAvailableSpaces();
                        }),
                        decrementCallback: () =>
                            createRoommateController.setState((s) {
                          s.decrementAvalableSpaces();
                        }),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      OnReactive(
                        () => TextFormField(
                          focusNode: priceController.focusNode,
                          controller: priceController.controller,
                          decoration: InputDecoration(
                            prefixIcon: Icon(
                              Icons.monetization_on,
                              color: AppColors.primaryColor,
                            ),
                            errorText: priceController.error,
                            // labelText: "Price",
                            hintText: "Price per roommate",
                            hintStyle: TextStyle(fontSize: 16),
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide:
                                    BorderSide(color: AppColors.primaryColor)),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: 50,
                      ),
                      // Text(createRoommateController.state.roommate.toString()),
                      Align(
                        child: CustomButtonWidget(
                          onPressed: () {
                            priceController.focusNode.requestFocus();
                            createRoommateController.setState(
                              (s) => s.valdateSecondScreen(),
                            );
                          },
                          text: "Continue",
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      )
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
