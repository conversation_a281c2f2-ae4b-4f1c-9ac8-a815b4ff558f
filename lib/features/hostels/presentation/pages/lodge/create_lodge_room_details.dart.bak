import 'package:campus_find/data/controllers/create_roomate_controller.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:campus_find/ui/app/common/custom_dropdown.dart';
import 'package:campus_find/ui/app/common/custom_select_chip.dart';
import 'package:flutter/material.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class CreateLodgeRoomDetails extends StatelessWidget {
  const CreateLodgeRoomDetails({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.black,
          onPressed: () => RM.navigate.back(),
        ),
      ),
      body: SizedBox.expand(
        child: OnReactive(
          () {
            final state = createRoommateController.state;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: <PERSON>umn(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SizedBox(height: 30),
                  Text(
                    "About the room",
                    style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 30),
                  CustomDropDown(
                    errorString: state.roomTypeError,
                    items: state.roomTypeList,
                    title: "Select Apartment Type",
                    value: state.roomType == '' ? null : state.roomType,
                    callback: (String? value) =>
                        createRoommateController.setState(
                      (s) {
                        s.setRoomType(value!);
                      },
                    ),
                  ),
                  SizedBox(height: 20),
                  CustomDropDown(
                    errorString: state.schoolError,
                    items: state.schoolNames,
                    title: "Select School",
                    value: state.school == '' ? null : state.school,
                    callback: (String? value) =>
                        createRoommateController.setState(
                      (s) {
                        s.setSchool(value!);
                      },
                    ),
                  ),
                  SizedBox(height: 20),
                  CustomDropDown(
                    errorString: state.areaError,
                    items: state.areaNames,
                    title: "Select Area",
                    value: state.area == '' ? null : state.area,
                    callback: (String? value) =>
                        createRoommateController.setState(
                      (s) {
                        s.setArea(value!);
                      },
                    ),
                  ),
                  // // TODO: Ensure accomodation spaces cannot exceed Number of roommate
                  // On(() => TextFormField(
                  //       controller: priceController.controller,
                  //       decoration: InputDecoration(
                  //         errorText: priceController.error,
                  //         labelText: "Price",
                  //         filled: true,
                  //         fillColor: Colors.white,
                  //         border: OutlineInputBorder(
                  //           borderRadius: BorderRadius.circular(10),
                  //         ),
                  //       ),
                  //     )).listenTo(priceController),
                  SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Select Features",
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 20),
                            Wrap(
                              spacing: 10,
                              children: state.features
                                  .map(
                                    (e) => CustomChip(
                                      isSelected:
                                          state.featuresList.contains(e),
                                      label: e,
                                      callback: state.featuresList.contains(e)
                                          ? () => createRoommateController
                                                  .setState((s) {
                                                s.featuresList.remove(e);
                                                print(s.featuresList);
                                              })
                                          : () =>
                                              createRoommateController.setState(
                                                (s) {
                                                  s.featuresList.add(e);
                                                  print(s.featuresList);
                                                },
                                              ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Spacer(flex: 2),
                  Align(
                    child: CustomButtonWidget(
                        onPressed:
                            // () => createRoommateController
                            //     .setState((s) => s.addNewRoommateRequest()),
                            () => createRoommateController
                                .setState((s) => s.validatedFirstScreen()),
                        text: "Continue"),
                  ),
                  Spacer(flex: 1)
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
