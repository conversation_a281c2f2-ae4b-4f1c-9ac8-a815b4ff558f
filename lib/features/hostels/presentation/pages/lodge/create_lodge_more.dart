import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CreateLodgeMore extends ConsumerStatefulWidget {
  const CreateLodgeMore({Key? key}) : super(key: key);

  @override
  ConsumerState<CreateLodgeMore> createState() => _CreateLodgeMoreState();
}

class _CreateLodgeMoreState extends ConsumerState<CreateLodgeMore> {
  final _formKey = GlobalKey<FormState>();
  final _amenitiesController = TextEditingController();
  final _rulesController = TextEditingController();

  @override
  void dispose() {
    _amenitiesController.dispose();
    _rulesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Lodge Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Additional Information',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              Si<PERSON>Box(height: 24),
              TextForm<PERSON>ield(
                controller: _amenitiesController,
                decoration: InputDecoration(
                  labelText: 'Amenities',
                  border: OutlineInputBorder(),
                  hintText: 'WiFi, Kitchen, Parking, etc.',
                ),
                maxLines: 3,
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _rulesController,
                decoration: InputDecoration(
                  labelText: 'House Rules',
                  border: OutlineInputBorder(),
                  hintText: 'No smoking, No pets, etc.',
                ),
                maxLines: 3,
              ),
              SizedBox(height: 24),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Availability',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      SizedBox(height: 8),
                      CheckboxListTile(
                        title: Text('Available immediately'),
                        value: true,
                        onChanged: (value) {},
                      ),
                      CheckboxListTile(
                        title: Text('Furnished'),
                        value: false,
                        onChanged: (value) {},
                      ),
                    ],
                  ),
                ),
              ),
              Spacer(),
              ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Lodge created successfully!')),
                  );
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    '/accommodation',
                    (route) => false,
                  );
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text('Create Lodge'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}