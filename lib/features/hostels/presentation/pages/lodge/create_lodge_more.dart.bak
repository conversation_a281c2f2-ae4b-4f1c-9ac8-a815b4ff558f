import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/create_roomate_controller.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class CreateLodgeMore extends StatelessWidget {
  const CreateLodgeMore({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.black,
          onPressed: () => RM.navigate.back(),
        ),
      ),
      body: SizedBox.expand(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SizedBox(height: 30),
                Text(
                  "About the room",
                  style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 40),
                OnReactive(
                  () => TextField(
                    focusNode: priceController.focusNode,
                    controller: priceController.controller,
                    onSubmitted: (s) =>
                        descriptionController.focusNode.requestFocus(),
                    decoration: InputDecoration(
                      prefixIcon: Icon(
                        Icons.monetization_on,
                        color: AppColors.primaryColor,
                      ),
                      errorText: priceController.error,
                      // labelText: "Price",
                      hintText: "Price per roommate",
                      hintStyle: TextStyle(fontSize: 16),
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide:
                              BorderSide(color: AppColors.primaryColor)),
                    ),
                  ),
                ),
                SizedBox(
                  height: 40,
                ),
                OnReactive(
                  () => SizedBox(
                    height: 300,
                    child: TextField(
                      keyboardType: TextInputType.multiline,
                      maxLines: null,
                      expands: true,
                      textAlignVertical: TextAlignVertical.top,
                      focusNode: descriptionController.focusNode,
                      controller: descriptionController.controller,
                      onSubmitted: (s) =>
                          descriptionController.focusNode.unfocus(),
                      decoration: InputDecoration(
                        // prefixIcon: Icon(
                        //   Icons.message,
                        //   color: AppColors.primaryColor,
                        // ),
                        errorText: priceController.error,
                        // labelText: "Price",
                        hintText: "Additional Information",
                        hintStyle: TextStyle(fontSize: 16),
                        filled: true,
                        fillColor: Colors.white,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10),
                            borderSide:
                                BorderSide(color: AppColors.primaryColor)),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 100),
                Align(
                  child: CustomButtonWidget(
                    onPressed: () {
                      descriptionController.focusNode.unfocus();
                      createRoommateController.setState(
                        (s) => s.valdateSecondScreen(),
                      );
                    },
                    text: "Continue",
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
