import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LodgeInterestPage extends ConsumerWidget {
  final String posterName;
  
  const LodgeInterestPage({
    Key? key,
    required this.posterName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Lodge Interest'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Lodge by $posterName',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    <PERSON><PERSON><PERSON><PERSON>(height: 8),
                    Text(
                      'Comfortable accommodation available',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 24),
            Text(
              'Contact the lodge owner:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            TextFormField(
              decoration: InputDecoration(
                labelText: 'Your message',
                border: OutlineInputBorder(),
                hintText: 'Hi, I\'m interested in your lodge...',
              ),
              maxLines: 4,
            ),
            Spacer(),
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Message sent successfully!')),
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text('Send Message'),
            ),
            SizedBox(height: 16),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('View Other Listings'),
            ),
          ],
        ),
      ),
    );
  }
}