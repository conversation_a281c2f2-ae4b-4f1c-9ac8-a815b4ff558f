import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class RequestInterestPage extends ConsumerWidget {
  final String posterName;
  
  const RequestInterestPage({
    Key? key,
    required this.posterName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Show Interest'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Request by $posterName',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Sized<PERSON>ox(height: 8),
                    Text(
                      'Looking for a roommate to share accommodation',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 24),
            Text(
              'Send a message to show your interest:',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            TextForm<PERSON>ield(
              decoration: InputDecoration(
                labelText: 'Your message',
                border: OutlineInputBorder(),
                hintText: 'Hi, I\'m interested in being your roommate...',
              ),
              maxLines: 4,
            ),
            Spacer(),
            ElevatedButton(
              onPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Interest sent successfully!')),
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: Text('Send Interest'),
            ),
            SizedBox(height: 16),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }
}