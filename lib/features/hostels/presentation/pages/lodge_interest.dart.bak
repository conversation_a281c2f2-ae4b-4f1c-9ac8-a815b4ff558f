import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class LodgeInterestPage extends StatelessWidget {
  LodgeInterestPage({Key? key, required this.posterName}) : super(key: key);

  final String posterName;

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Safe<PERSON>rea(
          child: SizedBox.expand(
              child: Column(
            children: [
              SizedBox(height: 80),
              Sized<PERSON>ox(
                height: MediaQuery.of(context).size.height * 0.4,
                child: Image.asset(
                  "assets/images/sent.jpg",
                  fit: BoxFit.fill,
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Text(
                "Lodge visit booked!",
                style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 60),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  "You will be contact by the agent via Whatsapp or email for further discussions",
                  style: TextStyle(fontSize: 20),
                  textAlign: TextAlign.center,
                ),
              ),
              //
              Spacer(
                flex: 2,
              ),
              CustomButtonWidget(
                text: "View other listings",
                onPressed: () => RM.navigate.back(),
              ),
              Spacer(
                flex: 1,
              ),
              // SizedBox(height: 20),
              // CustomButtonWidget(
              //   onPressed: () {},
              //   isOutlined: true,
              //   text: 'Check status',
              // ),
            ],
          )),
        ),
      ),
    );
  }
}
