import 'dart:io';

import 'package:campus_find/data/controllers/create_roomate_controller.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:campus_find/ui/app/common/image_card.dart';
import 'package:flutter/material.dart';
import 'package:image_picker_gallery_camera/image_picker_gallery_camera.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class CreateRequestAddImages extends StatelessWidget {
  const CreateRequestAddImages({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.black,
          onPressed: () => RM.navigate.back(),
        ),
      ),
      body: SafeArea(
        child: SizedBox.expand(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: OnReactive(
              () => Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SizedBox(height: 30),
                  Text(
                    "What does the room look like",
                    style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 10),
                  Text(
                    "Please add images showing the bedroom, kitchen and bathroom",
                    style: TextStyle(fontSize: 16, color: Colors.blueGrey),
                  ),
                  SizedBox(height: 40),
                  Wrap(
                    alignment: WrapAlignment.center,
                    children: [
                      for (File imageFile
                          in createRoommateController.state.imageFileList)
                        ImageCard(
                          imageFile: imageFile,
                          removeCallback: () =>
                              createRoommateController.setState(
                            (s) => s.removeImageFileFromList(imageFile),
                          ),
                        ),
                      createRoommateController.state.imageFileList.length == 4
                          ? SizedBox.shrink()
                          : InkWell(
                              onTap: () async {
                                final image = await ImagePickerGC.pickImage(
                                  context: context,
                                  source: ImgSource.Both,
                                );
                                createRoommateController.setState(
                                  (s) => s.addImageFileToList(
                                    File(image.path),
                                  ),
                                );
                              },
                              splashColor: Colors.blue,
                              child: Container(
                                height:
                                    MediaQuery.of(context).size.height * 0.22,
                                width: MediaQuery.of(context).size.width * 0.38,
                                decoration: BoxDecoration(
                                  color: Colors.grey,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Icon(
                                  Icons.add,
                                  color: Colors.white,
                                  size: 45,
                                ),
                              ),
                            ),
                    ],
                  ),
                  Spacer(
                    flex: 2,
                  ),
                  Align(
                    child: CustomButtonWidget(
                        onPressed: () => createRoommateController.state
                            .addNewRoommateRequest(),
                        text: "Finish"),
                  ),
                  Spacer(
                    flex: 1,
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
