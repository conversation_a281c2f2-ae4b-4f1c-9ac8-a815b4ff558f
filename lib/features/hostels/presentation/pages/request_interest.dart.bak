import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class RequestInterestPage extends StatelessWidget {
  RequestInterestPage({Key? key, required this.posterName}) : super(key: key);

  final String posterName;

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
          statusBarIconBrightness: Brightness.dark,
          statusBarColor: Colors.transparent),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: SizedBox.expand(
              child: Column(
            children: [
              SizedBox(height: 80),
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.4,
                child: Image.asset(
                  "assets/images/sent.jpg",
                  fit: BoxFit.fill,
                ),
              ),
              SizedBox(
                height: 20,
              ),
              Text(
                "Interest sent!",
                style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 120),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Text(
                  "Your interest to be $posterName's roommate has been sent.\n$posterName will contact you via whatsapp or via email soon.",
                  style: TextStyle(fontSize: 20),
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(
                height: 40,
              ),
              CustomButtonWidget(
                  text: "View other listings",
                  onPressed: () => RM.navigate.back())
            ],
          )),
        ),
      ),
    );
  }
}
