import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/accomodationController.dart';
import 'package:campus_find/data/models/accommodation.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

final currentIndex = 0.0.inj();

class RoommateDetailsPage extends StatelessWidget {
  final Accommodation _accommodation =
      accommodationController.state.currentlyViewed;
  final PageController controller = PageController(initialPage: 0);
  RoommateDetailsPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarBrightness: Brightness.dark,
        ),
        foregroundColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        elevation: 0,
        leadingWidth: 70,
        leading: IconButton(
          onPressed: () => RM.navigate.back(),
          icon: Icon(Icons.arrow_back_ios_outlined),
          color: Colors.white,
        ),
      ),
      body: SizedBox.expand(
        child: Stack(
          children: [
            Container(
              height: size.height,
              width: size.width,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Stack(
                      children: [
                        SizedBox(
                          height: size.height * 0.5,
                          width: size.width,
                          child: PageView(
                              scrollDirection: Axis.horizontal,
                              controller: controller,
                              onPageChanged: (index) {
                                currentIndex
                                    .setState((s) => s = index.toDouble());
                              },
                              children: _accommodation.images!
                                  .map((e) => _buildImage(link: e, size: size))
                                  .toList()),
                        ),
                        _buildIndicator(size)
                      ],
                    ),
                    _accommodation.map(
                        lodge: (lodge) => LodgeDetails(
                              lodge: lodge,
                            ),
                        roommate: (roommate) =>
                            RoommateRequestDetails(roommate: roommate))
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: _accommodation.map(
                    lodge: (lodge) => CustomButtonWidget(
                      onPressed: () =>
                          accommodationController.state.sendLodgeBooking(),
                      text: "Book a visit",
                    ),
                    roommate: (roommate) => CustomButtonWidget(
                      onPressed: () =>
                          accommodationController.state.sendRoommateInterest(),
                      text: "Interested?",
                    ),
                  )),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildImage({required String link, required Size size}) {
    return ClipRRect(
      borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
      child: SizedBox(
        height: size.height * 0.5,
        width: size.width,
        child: Hero(
          tag: _accommodation.id!,
          child: Image.network(
            link,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  Widget _buildIndicator(Size size) {
    return Positioned(
      top: size.height * 0.47,
      left: size.width * 0.45,
      child: OnReactive(
        () => DotsIndicator(
          dotsCount: _accommodation.images!.length,
          position: currentIndex.state,
          decorator: DotsDecorator(
            color: AppColors.secondaryColor, // Inactive color
            activeColor: Colors.blue,
            activeSize: const Size(18.0, 9.0),
            activeShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0)),
          ),
        ),
      ),
    );
  }
}

class LodgeDetails extends StatelessWidget {
  const LodgeDetails({Key? key, required this.lodge}) : super(key: key);

  final Lodge lodge;
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Padding(
      padding: const EdgeInsets.only(top: 20, left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: size.width,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MA,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      lodge.roomType!,
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    Gap(10),
                    Row(
                      children: [
                        Icon(Icons.location_on),
                        SizedBox(height: 20),
                        Text(
                          lodge.location!.area!,
                          style: TextStyle(fontSize: 20),
                        ),
                      ],
                    ),
                  ],
                ),
                Spacer(),
                Text(lodge.price!,
                    style:
                        TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          // Gap(30),
          SizedBox(height: 30),
          Text(
            "Description:",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 10,
          ),
          Text(
            lodge.description ?? '',
            style: TextStyle(fontSize: 17),
          ),
          Gap(30),
          Text(
            "Features:",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Gap(10),
          Wrap(
            children: lodge.features!
                .map((text) => FeaturesCard(text: text))
                .toList(),
          )
        ],
      ),
    );
  }
}

class RoommateRequestDetails extends StatelessWidget {
  const RoommateRequestDetails({Key? key, required this.roommate})
      : super(key: key);

  final Roommate roommate;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Padding(
      padding: const EdgeInsets.only(top: 20, left: 16, right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: size.width,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              // mainAxisAlignment: MA,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      roommate.roomType!,
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    Gap(10),
                    Text(
                      roommate.location!.area!,
                      style: TextStyle(fontSize: 20),
                    ),
                  ],
                ),
                Spacer(),
                Text(roommate.price!,
                    style:
                        TextStyle(fontSize: 26, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          Gap(30),
          SizedBox(
            // height: size.height * 0.1,
            width: size.width,
            child: Row(
              children: [
                _buildCard(name: "Gender", value: roommate.gender!),
                _buildCard(
                    name: "Available Spaces",
                    value: roommate.availableSpaces!.toString()),
                _buildCard(
                    name: "Total Roommates", value: roommate.numberOfRoomates!),
                _buildCard(name: "Level", value: roommate.level!)
              ],
            ),
          ),
          Gap(30),
          Text(
            "Other Features:",
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Gap(10),
          Wrap(
            children: roommate.features!
                .map((text) => FeaturesCard(text: text))
                .toList(),
          ),
          SizedBox(
            height: 70,
          )
        ],
      ),
    );
  }

  Widget _buildCard({required String name, required String value}) {
    return Container(
      width: 100,
      height: 150,
      margin: EdgeInsets.only(right: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 90,
            width: 90,
            decoration: BoxDecoration(
                border: Border.all(color: Color(0xFFf6f7fb), width: 3),
                borderRadius: BorderRadius.circular(20)),
            child: Center(
              child: Text(
                value,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          Gap(10),
          Text(
            name,
            style: TextStyle(
              fontSize: 18,
            ),
            softWrap: true,
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }
}

class FeaturesCard extends StatelessWidget {
  const FeaturesCard({Key? key, required this.text}) : super(key: key);

  final text;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.only(right: 10, top: 10),
      decoration: BoxDecoration(
          border: Border.all(width: 1),
          borderRadius: BorderRadius.circular(20)),
      child: Text(
        text,
        style: TextStyle(fontSize: 17),
      ),
    );
  }
}
