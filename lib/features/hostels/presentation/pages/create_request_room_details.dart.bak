import 'package:campus_find/data/controllers/create_roomate_controller.dart';
import 'package:campus_find/data/models/school.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:campus_find/ui/app/common/custom_dropdown.dart';
import 'package:campus_find/ui/app/common/custom_select_chip.dart';
import 'package:campus_find/ui/widgets/error_widget.dart';
import 'package:campus_find/ui/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class CreateRequestRoomDetails extends StatelessWidget {
  const CreateRequestRoomDetails({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final state = createRoommateController.state;
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          color: Colors.black,
          onPressed: () => RM.navigate.back(),
        ),
      ),
      body: SafeArea(
        child: SizedBox.expand(
          child: OnBuilder(
            listenTo: getSchools,
            builder: () => OnReactive(
              () => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    SizedBox(height: 30),
                    Text(
                      "About the room",
                      style:
                          TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 30),
                    CustomDropDown(
                      errorString: state.roomTypeError,
                      items: state.roomTypeList,
                      title: "Select Apartment Type",
                      value: state.roomType == '' ? null : state.roomType,
                      callback: (String? value) =>
                          createRoommateController.setState(
                        (s) {
                          s.setRoomType(value!);
                        },
                      ),
                    ),
                    SizedBox(height: 20),
                    CustomDropDown(
                      errorString: state.schoolError,
                      items: state.schoolNames,
                      title: "Select School",
                      value: state.school == '' ? null : state.school,
                      callback: (String? value) =>
                          createRoommateController.setState(
                        (s) {
                          s.setSchool(value!);
                        },
                      ),
                    ),
                    SizedBox(height: 20),
                    CustomDropDown(
                      errorString: state.areaError,
                      items: state.areaNames,
                      title: "Select Area",
                      value: state.area == '' ? null : state.area,
                      callback: (String? value) =>
                          createRoommateController.setState(
                        (s) {
                          s.setArea(value!);
                        },
                      ),
                    ),
                    // // TODO: Ensure accomodation spaces cannot exceed Number of roommate
                    // On(() => TextFormField(
                    //       controller: priceController.controller,
                    //       decoration: InputDecoration(
                    //         errorText: priceController.error,
                    //         labelText: "Price",
                    //         filled: true,
                    //         fillColor: Colors.white,
                    //         border: OutlineInputBorder(
                    //           borderRadius: BorderRadius.circular(10),
                    //         ),
                    //       ),
                    //     )).listenTo(priceController),
                    SizedBox(height: 20),
                    SizedBox(
                      width: double.infinity,
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Select Features",
                                style: TextStyle(
                                    fontSize: 20, fontWeight: FontWeight.bold),
                              ),
                              Gap(20),
                              Wrap(
                                spacing: 10,
                                children: state.features
                                    .map(
                                      (e) => CustomChip(
                                        isSelected:
                                            state.featuresList.contains(e),
                                        label: e,
                                        callback: state.featuresList.contains(e)
                                            ? () => createRoommateController
                                                    .setState((s) {
                                                  s.featuresList.remove(e);
                                                  print(s.featuresList);
                                                })
                                            : () => createRoommateController
                                                    .setState(
                                                  (s) {
                                                    s.featuresList.add(e);
                                                    print(s.featuresList);
                                                  },
                                                ),
                                      ),
                                    )
                                    .toList(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // Gap(20),
                    // Card(
                    //   child: Padding(
                    //     padding: EdgeInsets.all(20),
                    //     child: Column(
                    //       crossAxisAlignment: CrossAxisAlignment.start,
                    //       children: [
                    //         Text(
                    //           "Add Images",
                    //           style: TextStyle(
                    //               fontSize: 20, fontWeight: FontWeight.bold),
                    //         ),
                    //         Gap(20),
                    //         Row(
                    //           children: [
                    //             for (File imageFile in createRoommateController
                    //                 .state.imageFileList)
                    //               ImageCard(imageFile: imageFile),
                    //             createRoommateController
                    //                         .state.imageFileList.length ==
                    //                     3
                    //                 ? SizedBox.shrink()
                    //                 : InkWell(
                    //                     onTap: () async {
                    //                       PickedFile image =
                    //                           await ImagePickerGC.pickImage(
                    //                         context: context,
                    //                         source: ImgSource.Both,
                    //                       );
                    //                       createRoommateController.setState(
                    //                         (s) => s.addImageFileToList(
                    //                           File(image.path),
                    //                         ),
                    //                       );
                    //                     },
                    //                     splashColor: Colors.blue,
                    //                     child: Container(
                    //                       height: 100,
                    //                       width: 100,
                    //                       decoration: BoxDecoration(
                    //                         color: Colors.grey,
                    //                         borderRadius:
                    //                             BorderRadius.circular(10),
                    //                       ),
                    //                       child: Icon(
                    //                         Icons.add,
                    //                         color: Colors.white,
                    //                         size: 40,
                    //                       ),
                    //                     ),
                    //                   ),
                    //           ],
                    //         ),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                    Spacer(
                      flex: 2,
                    ),
                    Align(
                      child: CustomButtonWidget(
                          onPressed:
                              // () => createRoommateController
                              //     .setState((s) => s.addNewRoommateRequest()),
                              () => createRoommateController
                                  .setState((s) => s.validatedFirstScreen()),
                          text: "Continue"),
                    ),
                    Spacer(
                      flex: 1,
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Widget _dropDown(List<String> items, String title, String? value,
  //     void Function(String?)? callback) {
  //   children: <Widget>[
  //     Container(
  //       alignment: AlignmentDirectional.centerStart,
  //       margin: EdgeInsets.only(left: 4),
  //       child: Text(title),
  //     ),
  //     Padding(
  //       padding: const EdgeInsets.fromLTRB(0, 8, 0, 0),
  //       child: Card(
  //         child: Row(
  //           mainAxisSize: MainAxisSize.max,
  //           children: <Widget>[
  //             Expanded(
  //               child: Padding(
  //                 child: DirectSelectList<String>(
  //                   values: items,
  //                   defaultItemIndex: 0,
  //                   itemBuilder: (String value) => getDropDownMenuItem(value),
  //                   focusedItemDecoration: _getDslDecoration(),
  //                   onItemSelectedListener: (item, index, context) {
  //                     print(item);
  //                   },
  //                 ),
  //                 padding: EdgeInsets.only(left: 12),
  //               ),
  //             ),
  //             Padding(
  //               padding: EdgeInsets.only(right: 8),
  //               child: Icon(
  //                 Icons.unfold_more,
  //                 color: Colors.black38,
  //               ),
  //             )
  //           ],
  //         ),
  //       ),
  //     ),
  //   ],
  // );
  // }

  // DirectSelectItem<String> getDropDownMenuItem(String value) {
  //   return DirectSelectItem<String>(
  //       itemHeight: 56,
  //       value: value,
  //       itemBuilder: (context, value) {
  //         return Text(value);
  //       });
  // }

  // _getDslDecoration() {
  //   return BoxDecoration(
  //     border: BorderDirectional(
  //       bottom: BorderSide(width: 1, color: Colors.black12),
  //       top: BorderSide(width: 1, color: Colors.black12),
  //     ),
  //   );
  // }
}
