import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AccommodationHome extends ConsumerWidget {
  const AccommodationHome({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
      ),
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Text('Campus Find'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          foregroundColor: Colors.black,
          actions: [
            IconButton(
              onPressed: () {
                Navigator.pushNamed(context, '/notifications');
              },
              icon: Icon(Icons.notifications),
            ),
            IconButton(
              onPressed: () {
                Navigator.pushNamed(context, '/user');
              },
              icon: Icon(Icons.person),
            ),
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
          label: Text("Create", style: TextStyle(fontSize: 18)),
          backgroundColor: Color(0xFF5d5778),
          onPressed: () {
            _showCreateOptions(context);
          },
          icon: Icon(
            Icons.add,
            color: Colors.white,
          ),
        ),
        body: _buildBody(context),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Find Your Perfect Accommodation',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 8),
            Text(
              'Discover rooms, roommates, and lodges near your campus',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            SizedBox(height: 24),

            // Search Bar
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(context, '/search');
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(Icons.search, color: Colors.grey[600]),
                    SizedBox(width: 12),
                    Text(
                      'Search accommodations...',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24),

            // Categories
            Row(
              children: [
                Expanded(
                  child: _buildCategoryCard(
                    context,
                    'Roommates',
                    Icons.people,
                    'Find someone to share with',
                    () => Navigator.pushNamed(context, '/roommate_details'),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: _buildCategoryCard(
                    context,
                    'Lodges',
                    Icons.home,
                    'Browse available rooms',
                    () => ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Lodge listings coming soon!')),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 24),

            // Recent Listings
            Text(
              'Recent Listings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 16),

            Expanded(
              child: ListView.builder(
                itemCount: 5,
                itemBuilder: (context, index) {
                  return _buildListingCard(context, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    String title,
    IconData icon,
    String subtitle,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            SizedBox(height: 4),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListingCard(BuildContext context, int index) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).primaryColor,
          child: Text('${index + 1}'),
        ),
        title: Text('Accommodation ${index + 1}'),
        subtitle: Text('Looking for roommate • \$${(index + 1) * 100}/month'),
        trailing: Icon(Icons.arrow_forward_ios),
        onTap: () {
          Navigator.pushNamed(context, '/roommate_details');
        },
      ),
    );
  }

  void _showCreateOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'What would you like to create?',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              SizedBox(height: 24),
              ListTile(
                leading: Icon(Icons.person_add),
                title: Text('Roommate Request'),
                subtitle: Text('Find someone to share accommodation'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/create-new-rr');
                },
              ),
              ListTile(
                leading: Icon(Icons.home),
                title: Text('Lodge Listing'),
                subtitle: Text('List your accommodation'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/create_lodge_1');
                },
              ),
            ],
          ),
        );
      },
    );
  }
}
