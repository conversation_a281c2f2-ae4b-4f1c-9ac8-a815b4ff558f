import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/theme/text_styles.dart';
import '../../../../core/models/accommodation_model.dart';
import '../../../../core/models/search_criteria.dart';
import '../providers/search_provider.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/filter_bottom_sheet.dart';
import '../widgets/sort_bottom_sheet.dart';
import '../widgets/accommodation_list_widget.dart';
import '../widgets/search_suggestions_widget.dart';
import '../widgets/no_results_widget.dart';

class SearchPage extends ConsumerStatefulWidget {
  const SearchPage({Key? key}) : super(key: key);

  @override
  ConsumerState<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends ConsumerState<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  bool _showSuggestions = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(searchProvider.notifier).initialize();
    });

    _searchFocusNode.addListener(() {
      setState(() {
        _showSuggestions =
            _searchFocusNode.hasFocus && _searchController.text.isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Find Accommodation'),
        elevation: 0,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Consumer(
        builder: (context, ref, child) {
          final searchState = ref.watch(searchProvider);
          final searchNotifier = ref.read(searchProvider.notifier);
          return Column(
            children: [
              // Search header with filters
              Container(
                color: AppColors.primary,
                child: Column(
                  children: [
                    // Search bar
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: SearchBarWidget(
                        controller: _searchController,
                        focusNode: _searchFocusNode,
                        onChanged: (query) {
                          setState(() {
                            _showSuggestions =
                                query.isNotEmpty && _searchFocusNode.hasFocus;
                          });
                        },
                        onSubmitted: (query) {
                          searchNotifier.updateQuery(query);
                          _searchFocusNode.unfocus();
                          setState(() {
                            _showSuggestions = false;
                          });
                        },
                      ),
                    ),

                    // Filter and sort buttons
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 16, right: 16, bottom: 16),
                      child: Row(
                        children: [
                          Expanded(
                            child: _FilterButton(
                              onPressed: () => _showFilterBottomSheet(
                                  context, searchNotifier),
                              hasActiveFilters: _hasActiveFilters(
                                  searchState.currentCriteria),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _SortButton(
                              onPressed: () =>
                                  _showSortBottomSheet(context, searchNotifier),
                              currentSort: searchState.currentCriteria.sortBy,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Results section
              Expanded(
                child: Stack(
                  children: [
                    // Main content
                    _buildMainContent(searchState, searchNotifier),

                    // Search suggestions overlay
                    if (_showSuggestions)
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: SearchSuggestionsWidget(
                          suggestions: searchState.searchSuggestions,
                          onSuggestionTap: (suggestion) {
                            _searchController.text = suggestion;
                            searchNotifier.updateQuery(suggestion);
                            _searchFocusNode.unfocus();
                            setState(() {
                              _showSuggestions = false;
                            });
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainContent(
      SearchState searchState, SearchNotifier searchNotifier) {
    if (searchState.isLoading && searchState.accommodations.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (searchState.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: AppColors.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Something went wrong',
              style: AppTextStyles.heading3,
            ),
            const SizedBox(height: 8),
            Text(
              searchState.error!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                searchNotifier.clearError();
                searchNotifier.search(searchState.currentCriteria);
              },
              child: const Text('Try Again'),
            ),
          ],
        ),
      );
    }

    if (searchState.accommodations.isEmpty) {
      return NoResultsWidget(
        searchCriteria: searchState.currentCriteria,
        onClearFilters: () => searchNotifier.clearFilters(),
      );
    }

    return Column(
      children: [
        // Results count
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          color: AppColors.surface,
          child: Text(
            '${searchState.totalCount} accommodations found',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),

        // Accommodation list
        Expanded(
          child: AccommodationListWidget(
            accommodations: searchState.accommodations,
            isLoading: searchState.isLoading,
            hasMore: searchState.hasMore,
            onLoadMore: () => searchNotifier.loadMore(),
            onAccommodationTap: (accommodation) {
              Navigator.pushNamed(
                context,
                '/accommodation-detail',
                arguments: accommodation.id,
              );
            },
          ),
        ),
      ],
    );
  }

  void _showFilterBottomSheet(
      BuildContext context, SearchNotifier searchNotifier) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const FilterBottomSheet(),
    );
  }

  void _showSortBottomSheet(
      BuildContext context, SearchNotifier searchNotifier) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => const SortBottomSheet(),
    );
  }

  bool _hasActiveFilters(SearchCriteria criteria) {
    return criteria.location != null ||
        criteria.minPrice != null ||
        criteria.maxPrice != null ||
        criteria.amenities.isNotEmpty ||
        (criteria.type != null && criteria.type != AccommodationType.any) ||
        criteria.isVerified != null ||
        criteria.isAvailable != null;
  }
}

class _FilterButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool hasActiveFilters;

  const _FilterButton({
    required this.onPressed,
    required this.hasActiveFilters,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        Icons.filter_list,
        color: hasActiveFilters ? AppColors.primary : Colors.white,
      ),
      label: Text(
        'Filter',
        style: TextStyle(
          color: hasActiveFilters ? AppColors.primary : Colors.white,
        ),
      ),
      style: OutlinedButton.styleFrom(
        backgroundColor: hasActiveFilters ? Colors.white : Colors.transparent,
        side: BorderSide(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class _SortButton extends StatelessWidget {
  final VoidCallback onPressed;
  final SortOption currentSort;

  const _SortButton({
    required this.onPressed,
    required this.currentSort,
  });

  @override
  Widget build(BuildContext context) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: const Icon(
        Icons.sort,
        color: Colors.white,
      ),
      label: Text(
        _getSortLabel(currentSort),
        style: const TextStyle(color: Colors.white),
      ),
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  String _getSortLabel(SortOption sortOption) {
    switch (sortOption) {
      case SortOption.price:
        return 'Price';
      case SortOption.rating:
        return 'Rating';
      case SortOption.distance:
        return 'Distance';
      case SortOption.newest:
        return 'Newest';
      case SortOption.oldest:
        return 'Oldest';
      case SortOption.relevance:
        return 'Relevance';
    }
  }
}
