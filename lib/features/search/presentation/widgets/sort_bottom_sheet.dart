import 'package:flutter/material.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/theme/text_styles.dart';
import '../../../../core/models/search_criteria.dart';
import '../providers/search_provider.dart';

class SortBottomSheet extends StatefulWidget {
  final SearchProvider searchProvider;

  const SortBottomSheet({
    Key? key,
    required this.searchProvider,
  }) : super(key: key);

  @override
  State<SortBottomSheet> createState() => _SortBottomSheetState();
}

class _SortBottomSheetState extends State<SortBottomSheet> {
  late SortOption _selectedSortBy;
  late SortOrder _selectedSortOrder;

  @override
  void initState() {
    super.initState();
    final criteria = widget.searchProvider.currentCriteria;
    _selectedSortBy = criteria.sortBy;
    _selectedSortOrder = criteria.sortOrder;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Sort By',
                  style: AppTextStyles.heading2,
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: AppColors.textSecondary),
                  ),
                ),
              ],
            ),
          ),
          
          // Sort options
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildSortBySection(),
                  const Divider(height: 32),
                  _buildSortOrderSection(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
          
          // Apply button
          Container(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applySorting,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Apply Sorting',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortBySection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sort By',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          ...SortOption.values.map((option) => _buildSortByOption(option)),
        ],
      ),
    );
  }

  Widget _buildSortByOption(SortOption option) {
    return RadioListTile<SortOption>(
      title: Text(_getSortOptionLabel(option)),
      subtitle: Text(
        _getSortOptionDescription(option),
        style: AppTextStyles.bodySmall.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
      value: option,
      groupValue: _selectedSortBy,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedSortBy = value;
          });
        }
      },
      activeColor: AppColors.primary,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSortOrderSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order',
            style: AppTextStyles.heading3,
          ),
          const SizedBox(height: 16),
          RadioListTile<SortOrder>(
            title: const Text('Ascending'),
            subtitle: Text(
              _getSortOrderDescription(SortOrder.ascending),
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            value: SortOrder.ascending,
            groupValue: _selectedSortOrder,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedSortOrder = value;
                });
              }
            },
            activeColor: AppColors.primary,
            contentPadding: EdgeInsets.zero,
          ),
          RadioListTile<SortOrder>(
            title: const Text('Descending'),
            subtitle: Text(
              _getSortOrderDescription(SortOrder.descending),
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            value: SortOrder.descending,
            groupValue: _selectedSortOrder,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedSortOrder = value;
                });
              }
            },
            activeColor: AppColors.primary,
            contentPadding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  void _applySorting() {
    widget.searchProvider.updateSorting(_selectedSortBy, _selectedSortOrder);
    Navigator.pop(context);
  }

  String _getSortOptionLabel(SortOption option) {
    switch (option) {
      case SortOption.relevance:
        return 'Relevance';
      case SortOption.price:
        return 'Price';
      case SortOption.rating:
        return 'Rating';
      case SortOption.distance:
        return 'Distance';
      case SortOption.newest:
        return 'Newest';
      case SortOption.oldest:
        return 'Oldest';
    }
  }

  String _getSortOptionDescription(SortOption option) {
    switch (option) {
      case SortOption.relevance:
        return 'Best match for your search';
      case SortOption.price:
        return 'Monthly rent amount';
      case SortOption.rating:
        return 'User ratings and reviews';
      case SortOption.distance:
        return 'Distance from campus';
      case SortOption.newest:
        return 'Recently added accommodations';
      case SortOption.oldest:
        return 'Oldest accommodations first';
    }
  }

  String _getSortOrderDescription(SortOrder order) {
    switch (_selectedSortBy) {
      case SortOption.price:
        return order == SortOrder.ascending ? 'Lowest to highest' : 'Highest to lowest';
      case SortOption.rating:
        return order == SortOrder.ascending ? 'Lowest to highest' : 'Highest to lowest';
      case SortOption.distance:
        return order == SortOrder.ascending ? 'Nearest to farthest' : 'Farthest to nearest';
      case SortOption.newest:
      case SortOption.oldest:
        return order == SortOrder.ascending ? 'Oldest first' : 'Newest first';
      case SortOption.relevance:
        return order == SortOrder.ascending ? 'Less relevant first' : 'Most relevant first';
    }
  }
}