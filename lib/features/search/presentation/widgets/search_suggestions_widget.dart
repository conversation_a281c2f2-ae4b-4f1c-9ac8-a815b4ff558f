import 'package:flutter/material.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/theme/text_styles.dart';

class SearchSuggestionsWidget extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onSuggestionTap;

  const SearchSuggestionsWidget({
    Key? key,
    required this.suggestions,
    required this.onSuggestionTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(12)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.search,
                  size: 20,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Suggestions',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Suggestions list
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: suggestions.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Colors.grey[200],
            ),
            itemBuilder: (context, index) {
              final suggestion = suggestions[index];
              return ListTile(
                leading: Icon(
                  _getSuggestionIcon(suggestion),
                  size: 20,
                  color: AppColors.textSecondary,
                ),
                title: Text(
                  suggestion,
                  style: AppTextStyles.bodyMedium,
                ),
                onTap: () => onSuggestionTap(suggestion),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  IconData _getSuggestionIcon(String suggestion) {
    // Determine icon based on suggestion content
    final lowerSuggestion = suggestion.toLowerCase();

    if (lowerSuggestion.contains('apartment') ||
        lowerSuggestion.contains('room') ||
        lowerSuggestion.contains('hostel') ||
        lowerSuggestion.contains('studio')) {
      return Icons.home;
    } else if (lowerSuggestion.contains('university') ||
        lowerSuggestion.contains('college') ||
        lowerSuggestion.contains('campus')) {
      return Icons.school;
    } else if (lowerSuggestion.contains('wifi') ||
        lowerSuggestion.contains('security') ||
        lowerSuggestion.contains('parking') ||
        lowerSuggestion.contains('generator')) {
      return Icons.star_outline;
    } else if (lowerSuggestion.contains('lagos') ||
        lowerSuggestion.contains('abuja') ||
        lowerSuggestion.contains('ibadan') ||
        lowerSuggestion.contains('road') ||
        lowerSuggestion.contains('street')) {
      return Icons.location_on;
    } else {
      return Icons.search;
    }
  }
}
