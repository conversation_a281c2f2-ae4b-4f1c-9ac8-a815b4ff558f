import 'package:flutter/material.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/theme/text_styles.dart';
import '../../../../core/models/search_criteria.dart';

class NoResultsWidget extends StatelessWidget {
  final SearchCriteria searchCriteria;
  final VoidCallback onClearFilters;

  const NoResultsWidget({
    Key? key,
    required this.searchCriteria,
    required this.onClearFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // No results icon
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.surface,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search_off,
                size: 60,
                color: AppColors.textSecondary,
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Title
            Text(
              'No accommodations found',
              style: AppTextStyles.heading2,
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              _getNoResultsMessage(),
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 32),
            
            // Suggestions
            _buildSuggestions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestions(BuildContext context) {
    return Column(
      children: [
        Text(
          'Try these suggestions:',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Suggestion cards
        Wrap(
          spacing: 12,
          runSpacing: 12,
          alignment: WrapAlignment.center,
          children: [
            if (_hasActiveFilters())
              _buildSuggestionCard(
                icon: Icons.clear_all,
                title: 'Clear Filters',
                subtitle: 'Remove all filters',
                onTap: onClearFilters,
              ),
            
            _buildSuggestionCard(
              icon: Icons.location_city,
              title: 'Try Different Location',
              subtitle: 'Search in nearby areas',
              onTap: () {
                // This would typically trigger a location picker
                // For now, we'll just show a snackbar
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Try searching in different locations like Lagos, Abuja, or Ibadan'),
                  ),
                );
              },
            ),
            
            _buildSuggestionCard(
              icon: Icons.tune,
              title: 'Adjust Price Range',
              subtitle: 'Increase your budget',
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Try increasing your maximum price or removing price filters'),
                  ),
                );
              },
            ),
            
            _buildSuggestionCard(
              icon: Icons.home_work,
              title: 'Try Different Types',
              subtitle: 'Explore all accommodation types',
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Consider hostels, apartments, or shared rooms'),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSuggestionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 140,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: AppColors.primary,
                size: 24,
              ),
            ),
            
            const SizedBox(height: 12),
            
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 4),
            
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  String _getNoResultsMessage() {
    if (searchCriteria.query != null && searchCriteria.query!.isNotEmpty) {
      return 'We couldn\'t find any accommodations matching "${searchCriteria.query}". Try adjusting your search or filters.';
    } else if (_hasActiveFilters()) {
      return 'No accommodations match your current filters. Try adjusting your criteria or clearing some filters.';
    } else {
      return 'No accommodations are currently available. Please check back later or try different search criteria.';
    }
  }

  bool _hasActiveFilters() {
    return searchCriteria.location != null ||
        searchCriteria.minPrice != null ||
        searchCriteria.maxPrice != null ||
        searchCriteria.amenities.isNotEmpty ||
        (searchCriteria.type != null && searchCriteria.type != AccommodationType.any) ||
        searchCriteria.isVerified != null ||
        searchCriteria.isAvailable != null;
  }
}