import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/theme/text_styles.dart';
import '../../../../core/models/accommodation_model.dart';
import '../../../../core/models/search_criteria.dart';
import '../providers/search_provider.dart';

class FilterBottomSheet extends ConsumerStatefulWidget {
  const FilterBottomSheet({
    Key? key,
  }) : super(key: key);

  @override
  ConsumerState<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends ConsumerState<FilterBottomSheet> {
  late String? _selectedLocation;
  late double? _minPrice;
  late double? _maxPrice;
  late List<String> _selectedAmenities;
  late AccommodationType? _selectedType;
  late bool? _isVerified;
  late bool? _isAvailable;

  @override
  void initState() {
    super.initState();
    final criteria = ref.read(searchProvider).currentCriteria;
    _selectedLocation = criteria.location;
    _minPrice = criteria.minPrice;
    _maxPrice = criteria.maxPrice;
    _selectedAmenities = List.from(criteria.amenities);
    _selectedType = criteria.type;
    _isVerified = criteria.isVerified;
    _isAvailable = criteria.isAvailable;
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(searchProvider);
    final searchNotifier = ref.read(searchProvider.notifier);

    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: AppTextStyles.heading2,
                ),
                TextButton(
                  onPressed: _clearFilters,
                  child: Text(
                    'Clear All',
                    style: TextStyle(color: AppColors.primary),
                  ),
                ),
              ],
            ),
          ),

          // Filter content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLocationFilter(),
                  const SizedBox(height: 24),
                  _buildPriceRangeFilter(),
                  const SizedBox(height: 24),
                  _buildAccommodationTypeFilter(),
                  const SizedBox(height: 24),
                  _buildAmenitiesFilter(),
                  const SizedBox(height: 24),
                  _buildVerificationFilter(),
                  const SizedBox(height: 24),
                  _buildAvailabilityFilter(),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),

          // Apply button
          Container(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _applyFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Apply Filters',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationFilter() {
    final searchState = ref.watch(searchProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Location', style: AppTextStyles.heading3),
        const SizedBox(height: 12),
        if (searchState.filterOptions?.availableLocations != null)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: searchState.filterOptions!.availableLocations
                .map((location) => FilterChip(
                      label: Text(location),
                      selected: _selectedLocation == location,
                      onSelected: (selected) {
                        setState(() {
                          _selectedLocation = selected ? location : null;
                        });
                      },
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                    ))
                .toList(),
          ),
      ],
    );
  }

  Widget _buildPriceRangeFilter() {
    final searchState = ref.watch(searchProvider);
    final priceRange = searchState.filterOptions?.priceRange;
    if (priceRange == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Price Range (NGN)', style: AppTextStyles.heading3),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: _minPrice?.toString(),
                decoration: const InputDecoration(
                  labelText: 'Min Price',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _minPrice = double.tryParse(value);
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                initialValue: _maxPrice?.toString(),
                decoration: const InputDecoration(
                  labelText: 'Max Price',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  _maxPrice = double.tryParse(value);
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccommodationTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Accommodation Type', style: AppTextStyles.heading3),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AccommodationType.values
              .where((type) => type != AccommodationType.any)
              .map((type) => FilterChip(
                    label: Text(_getTypeLabel(type)),
                    selected: _selectedType == type,
                    onSelected: (selected) {
                      setState(() {
                        _selectedType = selected ? type : null;
                      });
                    },
                    selectedColor: AppColors.primary.withOpacity(0.2),
                    checkmarkColor: AppColors.primary,
                  ))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildAmenitiesFilter() {
    final searchState = ref.watch(searchProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Amenities', style: AppTextStyles.heading3),
        const SizedBox(height: 12),
        if (searchState.filterOptions?.availableAmenities != null)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: searchState.filterOptions!.availableAmenities
                .map((amenity) => FilterChip(
                      label: Text(amenity),
                      selected: _selectedAmenities.contains(amenity),
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedAmenities.add(amenity);
                          } else {
                            _selectedAmenities.remove(amenity);
                          }
                        });
                      },
                      selectedColor: AppColors.primary.withOpacity(0.2),
                      checkmarkColor: AppColors.primary,
                    ))
                .toList(),
          ),
      ],
    );
  }

  Widget _buildVerificationFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Verification Status', style: AppTextStyles.heading3),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('Verified only'),
          value: _isVerified ?? false,
          onChanged: (value) {
            setState(() {
              _isVerified = value == true ? true : null;
            });
          },
          activeColor: AppColors.primary,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  Widget _buildAvailabilityFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Availability', style: AppTextStyles.heading3),
        const SizedBox(height: 12),
        CheckboxListTile(
          title: const Text('Available only'),
          value: _isAvailable ?? false,
          onChanged: (value) {
            setState(() {
              _isAvailable = value == true ? true : null;
            });
          },
          activeColor: AppColors.primary,
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedLocation = null;
      _minPrice = null;
      _maxPrice = null;
      _selectedAmenities.clear();
      _selectedType = null;
      _isVerified = null;
      _isAvailable = null;
    });
  }

  void _applyFilters() {
    final searchState = ref.read(searchProvider);
    final searchNotifier = ref.read(searchProvider.notifier);
    final newCriteria = searchState.currentCriteria.copyWith(
      location: _selectedLocation,
      minPrice: _minPrice,
      maxPrice: _maxPrice,
      amenities: _selectedAmenities,
      type: _selectedType,
      isVerified: _isVerified,
      isAvailable: _isAvailable,
      offset: 0, // Reset pagination
    );

    searchNotifier.search(newCriteria);
    Navigator.pop(context);
  }

  String _getTypeLabel(AccommodationType type) {
    switch (type) {
      case AccommodationType.hostel:
        return 'Hostel';
      case AccommodationType.apartment:
        return 'Apartment';
      case AccommodationType.sharedRoom:
        return 'Shared Room';
      case AccommodationType.singleRoom:
        return 'Single Room';
      case AccommodationType.studio:
        return 'Studio';
      case AccommodationType.any:
        return 'Any';
    }
  }
}
