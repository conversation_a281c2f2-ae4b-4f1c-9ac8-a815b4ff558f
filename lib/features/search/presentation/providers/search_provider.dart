import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/accommodation_model.dart';
import '../../../../core/models/search_criteria.dart';
import '../../../hostels/data/accommodation_repository.dart';

// Provider for AccommodationRepository
final accommodationRepositoryProvider = Provider<AccommodationRepository>((ref) {
  return AccommodationRepository();
});

// Provider for SearchProvider
final searchProvider = StateNotifierProvider<SearchNotifier, SearchState>((ref) {
  final repository = ref.watch(accommodationRepositoryProvider);
  return SearchNotifier(repository);
});

// Search state class
class SearchState {
  final SearchResult? searchResult;
  final bool isLoading;
  final String? error;
  final FilterOptions? filterOptions;
  final SearchCriteria currentCriteria;
  final List<String> searchSuggestions;

  const SearchState({
    this.searchResult,
    this.isLoading = false,
    this.error,
    this.filterOptions,
    this.currentCriteria = const SearchCriteria(),
    this.searchSuggestions = const [],
  });

  // Getters for convenience
  List<AccommodationModel> get accommodations => searchResult?.accommodations ?? [];
  bool get hasMore => searchResult?.hasMore ?? false;
  int get totalCount => searchResult?.totalCount ?? 0;

  SearchState copyWith({
    SearchResult? searchResult,
    bool? isLoading,
    String? error,
    FilterOptions? filterOptions,
    SearchCriteria? currentCriteria,
    List<String>? searchSuggestions,
  }) {
    return SearchState(
      searchResult: searchResult ?? this.searchResult,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filterOptions: filterOptions ?? this.filterOptions,
      currentCriteria: currentCriteria ?? this.currentCriteria,
      searchSuggestions: searchSuggestions ?? this.searchSuggestions,
    );
  }
}

class SearchNotifier extends StateNotifier<SearchState> {
  final AccommodationRepository _repository;

  SearchNotifier(this._repository) : super(const SearchState());

  /// Initialize search provider and load filter options
  Future<void> initialize() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final filterOptions = await _repository.getFilterOptions();
      
      // Load initial results
      await search(state.currentCriteria);
      
      state = state.copyWith(filterOptions: filterOptions);
    } catch (e) {
      state = state.copyWith(error: 'Failed to initialize search: $e', isLoading: false);
    }
  }

  /// Perform search with given criteria
  Future<void> search(SearchCriteria criteria) async {
    try {
      state = state.copyWith(
        isLoading: true,
        error: null,
        currentCriteria: criteria,
      );

      final searchResult = await _repository.search(criteria);
      
      // Update search suggestions based on query
      List<String> suggestions = state.searchSuggestions;
      if (criteria.query != null && criteria.query!.isNotEmpty) {
        suggestions = _generateSearchSuggestions(criteria.query!, searchResult.accommodations);
      }
      
      state = state.copyWith(
        searchResult: searchResult,
        searchSuggestions: suggestions,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Search failed: $e',
        searchResult: null,
        isLoading: false,
      );
    }
  }

  /// Load more results for pagination
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;

    try {
      state = state.copyWith(isLoading: true);

      final nextCriteria = state.currentCriteria.copyWith(
        offset: state.currentCriteria.offset + state.currentCriteria.limit,
      );

      final nextResult = await _repository.search(nextCriteria);
      
      if (state.searchResult != null) {
        final updatedResult = state.searchResult!.copyWith(
          accommodations: [...state.searchResult!.accommodations, ...nextResult.accommodations],
          hasMore: nextResult.hasMore,
          criteria: nextCriteria,
        );
        
        state = state.copyWith(
          searchResult: updatedResult,
          currentCriteria: nextCriteria,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load more results: $e',
        isLoading: false,
      );
    }
  }

  /// Update search query
  void updateQuery(String query) {
    final newCriteria = state.currentCriteria.copyWith(
      query: query.isEmpty ? null : query,
      offset: 0, // Reset pagination
    );
    search(newCriteria);
  }

  /// Update location filter
  void updateLocation(String? location) {
    final newCriteria = state.currentCriteria.copyWith(
      location: location,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Update price range filter
  void updatePriceRange(double? minPrice, double? maxPrice) {
    final newCriteria = state.currentCriteria.copyWith(
      minPrice: minPrice,
      maxPrice: maxPrice,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Update accommodation type filter
  void updateAccommodationType(AccommodationType? type) {
    final newCriteria = state.currentCriteria.copyWith(
      type: type,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Update amenities filter
  void updateAmenities(List<String> amenities) {
    final newCriteria = state.currentCriteria.copyWith(
      amenities: amenities,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Update sorting
  void updateSorting(SortOption sortBy, SortOrder sortOrder) {
    final newCriteria = state.currentCriteria.copyWith(
      sortBy: sortBy,
      sortOrder: sortOrder,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Update verification filter
  void updateVerificationFilter(bool? isVerified) {
    final newCriteria = state.currentCriteria.copyWith(
      isVerified: isVerified,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Update availability filter
  void updateAvailabilityFilter(bool? isAvailable) {
    final newCriteria = state.currentCriteria.copyWith(
      isAvailable: isAvailable,
      offset: 0,
    );
    search(newCriteria);
  }

  /// Clear all filters
  void clearFilters() {
    const clearedCriteria = SearchCriteria();
    search(clearedCriteria);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Generate search suggestions based on query
  List<String> _generateSearchSuggestions(String query, List<AccommodationModel> accommodations) {
    if (query.length < 2) {
      return [];
    }

    final suggestions = <String>{};
    
    // Add suggestions from accommodation titles and descriptions
    for (final accommodation in accommodations) {
      if (accommodation.title.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(accommodation.title);
      }
      
      // Add location suggestions
      if (accommodation.location.address.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(accommodation.location.address);
      }
      
      if (accommodation.location.city != null &&
          accommodation.location.city!.toLowerCase().contains(query.toLowerCase())) {
        suggestions.add(accommodation.location.city!);
      }
      
      // Add amenity suggestions
      for (final amenity in accommodation.amenities) {
        if (amenity.toLowerCase().contains(query.toLowerCase())) {
          suggestions.add(amenity);
        }
      }
    }

    return suggestions.take(5).toList();
  }

  /// Get accommodation by ID
  Future<AccommodationModel?> getAccommodationById(String id) async {
    try {
      return await _repository.getById(id);
    } catch (e) {
      state = state.copyWith(error: 'Failed to get accommodation details: $e');
      return null;
    }
  }
}