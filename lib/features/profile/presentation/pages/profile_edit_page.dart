import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../providers/profile_provider.dart';
import '../widgets/profile_image_picker.dart';
import '../widgets/profile_form_fields.dart';

class ProfileEditPage extends ConsumerStatefulWidget {
  const ProfileEditPage({super.key});

  @override
  ConsumerState<ProfileEditPage> createState() => _ProfileEditPageState();
}

class _ProfileEditPageState extends ConsumerState<ProfileEditPage> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _campusController = TextEditingController();
  
  UserPreferences? _preferences;
  bool _receiveNotifications = true;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneNumberController.dispose();
    _campusController.dispose();
    super.dispose();
  }

  void _loadUserProfile() {
    final authService = ref.read(authServiceProvider);
    final currentUser = authService.currentUser;
    
    if (currentUser != null) {
      ref.read(profileProvider.notifier).loadUserProfile(currentUser.uid);
    }
  }

  void _populateFields(UserModel user) {
    _fullNameController.text = user.fullName;
    _phoneNumberController.text = user.phoneNumber ?? '';
    _campusController.text = user.campus ?? '';
    _preferences = user.preferences;
    _receiveNotifications = user.preferences.receiveNotifications;
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    final authService = ref.read(authServiceProvider);
    final currentUser = authService.currentUser;
    
    if (currentUser == null) {
      _showErrorSnackBar('User not authenticated');
      return;
    }

    final profileState = ref.read(profileProvider);
    if (profileState.user == null) {
      _showErrorSnackBar('Profile not loaded');
      return;
    }

    try {
      final updatedPreferences = (_preferences ?? const UserPreferences()).copyWith(
        receiveNotifications: _receiveNotifications,
      );

      final updatedUser = profileState.user!.copyWith(
        fullName: _fullNameController.text.trim(),
        phoneNumber: _phoneNumberController.text.trim().isEmpty 
            ? null 
            : _phoneNumberController.text.trim(),
        campus: _campusController.text.trim().isEmpty 
            ? null 
            : _campusController.text.trim(),
        preferences: updatedPreferences,
      );

      await ref.read(profileProvider.notifier).updateUserProfile(
        currentUser.uid,
        updatedUser,
      );

      if (mounted) {
        _showSuccessSnackBar('Profile updated successfully');
        Navigator.of(context).pop();
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update profile: $e');
    }
  }

  Future<void> _updateProfileImage(XFile imageFile) async {
    final authService = ref.read(authServiceProvider);
    final currentUser = authService.currentUser;
    
    if (currentUser == null) {
      _showErrorSnackBar('User not authenticated');
      return;
    }

    try {
      await ref.read(profileProvider.notifier).updateProfileImage(
        currentUser.uid,
        imageFile,
      );
      
      if (mounted) {
        _showSuccessSnackBar('Profile image updated successfully');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to update profile image: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final profileState = ref.watch(profileProvider);
    
    // Populate fields when user data is loaded
    if (profileState.user != null && _fullNameController.text.isEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _populateFields(profileState.user!);
      });
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Profile'),
        elevation: 0,
        actions: [
          if (profileState.isUpdating)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ),
        ],
      ),
      body: profileState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : profileState.error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error: ${profileState.error}',
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadUserProfile,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Profile Image Section
                        ProfileImagePicker(
                          imageUrl: profileState.user?.profileImageUrl,
                          onImageSelected: _updateProfileImage,
                          isLoading: profileState.isUpdating,
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // Form Fields
                        ProfileFormFields(
                          fullNameController: _fullNameController,
                          phoneNumberController: _phoneNumberController,
                          campusController: _campusController,
                          receiveNotifications: _receiveNotifications,
                          onNotificationChanged: (value) {
                            setState(() {
                              _receiveNotifications = value;
                            });
                          },
                        ),
                        
                        const SizedBox(height: 32),
                        
                        // Save Button
                        CustomButton(
                          text: 'Save Changes',
                          onPressed: profileState.isUpdating ? null : _saveProfile,
                          isLoading: profileState.isUpdating,
                        ),
                      ],
                    ),
                  ),
                ),
    );
  }
}