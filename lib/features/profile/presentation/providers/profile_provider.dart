import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/models/user_model.dart';
import '../../data/profile_service.dart';

/// Profile state
class ProfileState {
  final UserModel? user;
  final bool isLoading;
  final String? error;
  final bool isUpdating;

  const ProfileState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isUpdating = false,
  });

  ProfileState copyWith({
    UserModel? user,
    bool? isLoading,
    String? error,
    bool? isUpdating,
  }) {
    return ProfileState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isUpdating: isUpdating ?? this.isUpdating,
    );
  }
}

/// Profile state notifier
class ProfileNotifier extends StateNotifier<ProfileState> {
  final ProfileService _profileService;

  ProfileNotifier(this._profileService) : super(const ProfileState());

  /// Load user profile
  Future<void> loadUserProfile(String userId) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final user = await _profileService.getUserProfile(userId);
      state = state.copyWith(
        user: user,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(String userId, UserModel updatedUser) async {
    state = state.copyWith(isUpdating: true, error: null);
    
    try {
      await _profileService.updateUserProfile(userId, updatedUser);
      state = state.copyWith(
        user: updatedUser,
        isUpdating: false,
      );
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Update profile fields
  Future<void> updateProfileFields(String userId, Map<String, dynamic> fields) async {
    state = state.copyWith(isUpdating: true, error: null);
    
    try {
      await _profileService.updateProfileFields(userId, fields);
      
      // Update local state if user exists
      if (state.user != null) {
        final updatedUserJson = {...state.user!.toJson(), ...fields};
        final updatedUser = UserModel.fromJson(updatedUserJson);
        state = state.copyWith(
          user: updatedUser,
          isUpdating: false,
        );
      } else {
        state = state.copyWith(isUpdating: false);
      }
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Update profile image
  Future<void> updateProfileImage(String userId, XFile imageFile) async {
    state = state.copyWith(isUpdating: true, error: null);
    
    try {
      final oldImageUrl = state.user?.profileImageUrl;
      final newImageUrl = await _profileService.updateProfileImage(
        userId, 
        imageFile, 
        oldImageUrl: oldImageUrl,
      );
      
      // Update local state
      if (state.user != null) {
        final updatedUser = state.user!.copyWith(profileImageUrl: newImageUrl);
        state = state.copyWith(
          user: updatedUser,
          isUpdating: false,
        );
      } else {
        state = state.copyWith(isUpdating: false);
      }
    } catch (e) {
      state = state.copyWith(
        isUpdating: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Reset state
  void reset() {
    state = const ProfileState();
  }
}

/// Profile provider
final profileProvider = StateNotifierProvider<ProfileNotifier, ProfileState>((ref) {
  final profileService = ref.watch(profileServiceProvider);
  return ProfileNotifier(profileService);
});

/// Stream profile provider - for real-time updates
final streamProfileProvider = StreamProvider.family<UserModel?, String>((ref, userId) {
  final profileService = ref.watch(profileServiceProvider);
  return profileService.streamUserProfile(userId);
});