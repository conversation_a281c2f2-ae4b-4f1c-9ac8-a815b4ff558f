import 'package:flutter/material.dart';

class ProfileFormFields extends StatelessWidget {
  final TextEditingController fullNameController;
  final TextEditingController phoneNumberController;
  final TextEditingController campusController;
  final bool receiveNotifications;
  final Function(bool) onNotificationChanged;

  const ProfileFormFields({
    super.key,
    required this.fullNameController,
    required this.phoneNumberController,
    required this.campusController,
    required this.receiveNotifications,
    required this.onNotificationChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Full Name Field
        TextFormField(
          controller: fullNameController,
          decoration: const InputDecoration(
            labelText: 'Full Name',
            hintText: 'Enter your full name',
            prefixIcon: Icon(Icons.person_outline),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Full name is required';
            }
            if (value.trim().length < 2) {
              return 'Full name must be at least 2 characters long';
            }
            return null;
          },
          textCapitalization: TextCapitalization.words,
        ),
        
        const SizedBox(height: 16),
        
        // Phone Number Field
        TextFormField(
          controller: phoneNumberController,
          decoration: const InputDecoration(
            labelText: 'Phone Number',
            hintText: 'Enter your phone number',
            prefixIcon: Icon(Icons.phone_outlined),
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              // Remove all non-digit characters
              final digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');
              if (digitsOnly.length < 10 || digitsOnly.length > 15) {
                return 'Please enter a valid phone number';
              }
            }
            return null;
          },
        ),
        
        const SizedBox(height: 16),
        
        // Campus Field
        TextFormField(
          controller: campusController,
          decoration: const InputDecoration(
            labelText: 'Campus/University',
            hintText: 'Enter your campus or university name',
            prefixIcon: Icon(Icons.school_outlined),
            border: OutlineInputBorder(),
          ),
          textCapitalization: TextCapitalization.words,
        ),
        
        const SizedBox(height: 24),
        
        // Notifications Section
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Notification Preferences',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                SwitchListTile(
                  title: const Text('Receive Notifications'),
                  subtitle: const Text(
                    'Get notified about accommodation updates, favorites, and important announcements',
                  ),
                  value: receiveNotifications,
                  onChanged: onNotificationChanged,
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Profile Completion Info
        Card(
          color: Colors.blue.shade50,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade700,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Complete your profile to get personalized accommodation recommendations and better search results.',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}