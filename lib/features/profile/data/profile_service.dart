import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import '../../../core/config/riverpod_config.dart';
import '../../../core/models/user_model.dart';

/// Profile service provider
final profileServiceProvider = Provider<ProfileService>((ref) {
  final firestore = ref.watch(firestoreProvider);
  final storage = ref.watch(firebaseStorageProvider);
  return ProfileService(firestore, storage);
});

/// Service for managing user profile operations
class ProfileService {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final ImagePicker _imagePicker = ImagePicker();

  ProfileService(this._firestore, this._storage);

  /// Get user profile by ID
  Future<UserModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists && doc.data() != null) {
        return UserModel.fromJson(doc.data()!);
      }
      return null;
    } catch (e) {
      throw ProfileException('Failed to get user profile: $e');
    }
  }

  /// Update user profile
  Future<void> updateUserProfile(String userId, UserModel updatedUser) async {
    try {
      // Validate the user model before updating
      final validationErrors = updatedUser.validate();
      if (validationErrors.isNotEmpty) {
        throw ProfileException('Validation failed: ${validationErrors.join(', ')}');
      }

      await _firestore.collection('users').doc(userId).update({
        ...updatedUser.toJson(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (e is ProfileException) rethrow;
      throw ProfileException('Failed to update user profile: $e');
    }
  }

  /// Update specific profile fields
  Future<void> updateProfileFields(String userId, Map<String, dynamic> fields) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        ...fields,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw ProfileException('Failed to update profile fields: $e');
    }
  }

  /// Pick image from gallery
  Future<XFile?> pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      throw ProfileException('Failed to pick image from gallery: $e');
    }
  }

  /// Pick image from camera
  Future<XFile?> pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );
      return image;
    } catch (e) {
      throw ProfileException('Failed to pick image from camera: $e');
    }
  }

  /// Upload profile image to Firebase Storage
  Future<String> uploadProfileImage(String userId, XFile imageFile) async {
    try {
      final File file = File(imageFile.path);
      final String fileName = 'profile_images/$userId/${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      final Reference ref = _storage.ref().child(fileName);
      final UploadTask uploadTask = ref.putFile(file);
      
      final TaskSnapshot snapshot = await uploadTask;
      final String downloadUrl = await snapshot.ref.getDownloadURL();
      
      return downloadUrl;
    } catch (e) {
      throw ProfileException('Failed to upload profile image: $e');
    }
  }

  /// Delete profile image from Firebase Storage
  Future<void> deleteProfileImage(String imageUrl) async {
    try {
      if (imageUrl.isNotEmpty) {
        final Reference ref = _storage.refFromURL(imageUrl);
        await ref.delete();
      }
    } catch (e) {
      // Don't throw error if image doesn't exist
      print('Warning: Could not delete profile image: $e');
    }
  }

  /// Update profile image
  Future<String> updateProfileImage(String userId, XFile imageFile, {String? oldImageUrl}) async {
    try {
      // Upload new image
      final String newImageUrl = await uploadProfileImage(userId, imageFile);
      
      // Update user profile with new image URL
      await updateProfileFields(userId, {'profileImageUrl': newImageUrl});
      
      // Delete old image if it exists
      if (oldImageUrl != null && oldImageUrl.isNotEmpty) {
        await deleteProfileImage(oldImageUrl);
      }
      
      return newImageUrl;
    } catch (e) {
      throw ProfileException('Failed to update profile image: $e');
    }
  }

  /// Stream user profile changes
  Stream<UserModel?> streamUserProfile(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((doc) {
      if (doc.exists && doc.data() != null) {
        return UserModel.fromJson(doc.data()!);
      }
      return null;
    });
  }
}

/// Custom exception for profile operations
class ProfileException implements Exception {
  final String message;
  
  ProfileException(this.message);
  
  @override
  String toString() => 'ProfileException: $message';
}