import 'dart:io';

import 'package:campus_find/data/controllers/api_service.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/data/controllers/create_roomate_controller.dart';
import 'package:campus_find/data/models/user.dart';
import 'package:campus_find/ui/app/common/custom_button.dart';
import 'package:campus_find/ui/app/common/custom_dropdown.dart';
import 'package:campus_find/ui/auth/login_page.dart';
import 'package:campus_find/ui/widgets/error_widget.dart';
import 'package:campus_find/ui/widgets/loading_widget.dart';
import 'package:campus_find/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:form_field_validator/form_field_validator.dart';
import 'package:image_picker_gallery_camera/image_picker_gallery_camera.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

final requiredValidator =
    RequiredValidator(errorText: 'this field is required');

class UploadImage extends StatelessWidget {
  UploadImage({Key? key}) : super(key: key);

  final school = "".inj();
  final imagePath = "".inj();
  final _phoneNumber = RM.injectTextEditing();
  final _name = RM.injectTextEditing();
  String schoolError = '';

  @override
  Widget build(BuildContext context) {
    return Placeholder();
  }
}

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: SizedBox.expand(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 20),
//           child: OnBuilder(
//             listenTo: getSchools,
//             builder: () {
//               if (getSchools.isWaiting) {
//                 return LoadingWidget();
//               }
//               if (getSchools.hasError) {
//                 return AppErrorWidget(error: getSchools.error.toString());
//               }
//               final data = getSchools.state;
//               List<String> schoolNames = data.map((e) => e.name).toList();
//               return SingleChildScrollView(
//                 child: Column(
//                   children: [
//                     SizedBox(height: 100),
//                     OnBuilder(
//                       listenTo: imagePath,
//                       builder: () {
//                         if (imagePath.state == "")
//                           return GestureDetector(
//                               onTap: _setImage,
//                               child: Container(
//                                 height: 200,
//                                 width: 200,
//                                 decoration: BoxDecoration(
//                                     shape: BoxShape.circle,
//                                     border: Border.all()),
//                                   child: Icon(
//                                   Icons.add_a_photo_outlined,
//                                   size: 40,
//                                   ),
//                                 ),
//                               );

//                           return CircleAvatar(
//                             radius: 100,
//                               backgroundColor: Colors.white,
//                               backgroundImage: FileImage(
//                                 File(imagePath.state),
//                               ),
//                             );
//                           }
//                         },
//                       ),
//                     SizedBox(height: 100),
//                     OnBuilder(
//                       listenTo: _name,
//                       builder: () {
//                         return TextFormField(
//                           controller: _name.controller,
//                           decoration: InputDecoration(
//                             labelText: "Full Name",
//                             labelStyle: TextStyle(color: Color(0xFF2D2943)),
//                             focusedBorder: OutlineInputBorder(
//                               borderSide: BorderSide(color: Color(0xFF2D2943)),
//                               borderRadius: BorderRadius.circular(10),
//                             ),
//                             errorText: _name.error,
//                           ),
//                           validator: requiredValidator,
//                         );
//                       },
//                     ),
//                     SizedBox(
//                       height: 20,
//                     ),
//                     OnBuilder(
//                       listenTo: _phoneNumber,
//                       builder: () {
//                         return TextFormField(
//                           controller: _phoneNumber.controller,
//                           decoration: InputDecoration(
//                             labelText: "Phone Number",
//                             labelStyle: TextStyle(color: Color(0xFF2D2943)),
//                             focusedBorder: OutlineInputBorder(
//                               borderSide: BorderSide(color: Color(0xFF2D2943)),
//                               borderRadius: BorderRadius.circular(10),
//                             ),
//                             errorText: _phoneNumber.error,
//                           ),
//                           validator: requiredValidator,
//                         );
//                       },
//                     ),
//                     SizedBox(height: 40),
//                     OnBuilder(
//                       listenTo: school,
//                       builder: () {
//                         return CustomDropDown(
//                           callback: (val) => school.setState((s) {
//                                 schoolError = '';
//                                 return val ?? "";
//                               }),
//                           value: school.state == "" ? null : school.state,
//                           items: schoolNames,
//                           title: "Select a school",
//                           errorString: schoolError),
//                     }),
//                     SizedBox(
//                       height: 70,
//                     ),
//                     CustomButtonWidget(
//                       onPressed: _finishReg,
//                       text: "Finish Registration",
//                     ),
//                   ],
//                 ),
//               );
//             },
//           )
//         ),
//       ),
//     );
//   }

//   void _setImage() async {
//     final image = await ImagePickerGC.pickImage(
//         context: RM.context!, source: ImgSource.Both, imageQuality: 60);
//     print(image.path);
//     imagePath.setState((s) => s = image.path);
//   }

//   void _finishReg() async {
//     if (imagePath.state == '') {
//       Utils.showErrorSnackbar("Please select an image");
//       return;
//     }
//     if (school.state.isEmpty) {
//       schoolError = "Please select a school";
//       school.notify();
//       return;
//     }
//     if (!_phoneNumber.validate() || !_name.validate()) {
//       return;
//     }

//     Utils.showLoadingWidget(title: "Finishng up...");
//     String imgUrl = await apiService.state.uploadImage(File(imagePath.state));
//     User _user = user.state!.copyWith(
//         school: school.state,
//         photoUrl: imgUrl,
//         name: _name.text,
//         phone: "+234${_phoneNumber.state.trim().substring(1)}");
//     await apiService.state.saveUser(_user);
//     user.state = _user;
//   }
// }
