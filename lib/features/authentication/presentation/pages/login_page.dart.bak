// import 'package:auth_buttons/auth_buttons.dart';
import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/data/exceptions/sign_in_exception.dart';
import 'package:campus_find/data/models/auth.dart';
import 'package:campus_find/data/navigation/routes.dart';
import 'package:campus_find/ui/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:form_field_validator/form_field_validator.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

final requiredValidator =
    RequiredValidator(errorText: 'this field is required');

final _email = RM.injectTextEditing(
  validateOnLoseFocus: true,
);
final _password = RM.injectTextEditing(
  validateOnLoseFocus: true,
);

final form = RM.injectForm(
    autoFocusOnFirstError: true,
    submit: () async {
      RM.navigate.toDialog(
        SimpleDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 30, vertical: 30),
          children: [LoadingWidget(text: "Getting you right in...")],
        ),
        barrierDismissible: false,
      );
      print("Email: ${_email.state}, Pasword: ${_password.state}");
      await user.auth.signIn(
        (_) => AuthParam(
          email: _email.state,
          password: _password.state,
          signIn: SignIn.withEmailAndPassword,
        ),
      );
      if (user.error is EmailException) {
        _email.error = user.error.message;
      }
      if (user.error is PasswordException) {
        _password.error = user.error.message;
      }
    },
    onSubmitting: () {
      // called while waiting for form submission,
    },
    onSubmitted: () {
      RM.navigate.back();
      // called after form is successfully submitted
      // For example navigation to user page
    });

class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          ClipPath(
            clipper: MyClipper(),
            child: Container(
              width: size.width,
              height: size.height / 2,
              decoration: BoxDecoration(
                  // gradient: LinearGradient(
                  //   colors: [Color(0xFF5d5778), Color(0xFF2d2942)],
                  //   begin: Alignment.topLeft,
                  //   end: Alignment.bottomRight,
                  // ),
                  color: AppColors.primaryColor),
              child: Padding(
                padding: const EdgeInsets.only(top: 70, left: 30),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Welcome back to",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 30,
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.italic),
                    ),
                    SizedBox(height: 10),
                    Text(
                      "Campus find",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 45,
                          fontWeight: FontWeight.bold,
                          fontFamily: "Montserrat"),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Center(
            child: Container(
              padding: EdgeInsets.only(left: 30, right: 30, top: 300),
              decoration: BoxDecoration(
                  // gradient: LinearGradient(
                  //   colors: [Color(0xFF5d5778), Colors.white],
                  //   begin: Alignment.topLeft,
                  //   end: Alignment.bottomRight,
                  // ),
                  ),
              child: SingleChildScrollView(
                child: OnFormBuilder(
                  listenTo: form,
                  builder: () => Column(
                    children: [
                      TextFormField(
                        autofocus: true,
                        controller: _email.controller,
                        focusNode: _email.focusNode,
                        onEditingComplete: () =>
                            _password.focusNode.requestFocus(),
                        decoration: getInputDecor("Email", _email.error),
                        validator: EmailValidator(
                            errorText: 'enter a valid email address'),
                      ),
                      SizedBox(height: 40),
                      TextFormField(
                        controller: _password.controller,
                        focusNode: _password.focusNode,
                        decoration: getInputDecor("Password", _password.error),
                        obscureText: true,
                        validator: MultiValidator([
                          requiredValidator,
                          MinLengthValidator(5,
                              errorText: "Enter a password greater than 5")
                        ]),
                      ),
                      SizedBox(
                        height: 70,
                      ),
                      loginButton(),
                      SizedBox(height: 20),
                      registerButton(context),
                      SizedBox(
                        height: 20,
                      ),
                      forgotPasswordButton()
                      // googleButton(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget registerButton(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          "New?",
          style: TextStyle(fontSize: 20),
        ),
        TextButton(
          onPressed: () {
            _email.focusNode.unfocus();
            _password.focusNode.unfocus();
            RM.navigate.toNamed(Routes.REGISTER);
          },
          child: Text(
            "Sign up",
            style: TextStyle(fontSize: 20),
          ),
        )
      ],
    );
  }

  Widget loginButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          elevation: 10,
          shadowColor: AppColors.secondaryColor,
          shape: StadiumBorder(),
          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
        ),
        onPressed: () {
          _email.focusNode.unfocus();
          _password.focusNode.unfocus();
          if (form.validate()) {
            form.submit();
          }
        },
        child: Text(
          "Login",
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget forgotPasswordButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        "Forgot password?",
        style: TextStyle(fontSize: 18, color: Colors.redAccent),
      ),
    );
  }

  // Widget googleButton() {
  //   return GoogleAuthButton(
  //     onPressed: () async {
  //       _showMyDialog();
  //       String message = await auth.signInWithGoogle();
  //       if (message == "Signed In") {
  //         await Provider.of<UserController>(context, listen: false).initUser();
  //       }
  //     },
  //   );
  // }

  InputDecoration getInputDecor(String text, String? errorText) {
    InputDecoration _inputDecoration = InputDecoration(
        labelText: text,
        labelStyle: TextStyle(color: Color(0xFF2D2943)),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Color(0xFF2D2943)),
          borderRadius: BorderRadius.circular(10),
        ),
        errorText: errorText);
    return _inputDecoration;
  }
}

class LoginForm extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return OnFormBuilder(
      listenTo: form,
      builder: () => Column(
        children: <Widget>[
          TextField(
            focusNode: _email.focusNode,
            controller: _email.controller,
            decoration: InputDecoration(
              errorText: _email.error,
            ),
            onSubmitted: (_) {
              //request the password node
              _password.focusNode.requestFocus();
            },
          ),
          TextField(
            focusNode: _password.focusNode,
            controller: _password.controller,
            decoration: new InputDecoration(
              errorText: _password.error,
            ),
            onSubmitted: (_) {
              //request the submit button node
              form.submitFocusNode.requestFocus();
            },
          ),
          OnFormSubmissionBuilder(
            listenTo: form,
            onSubmitting: () => Text("Submitting"),
            child: ElevatedButton(
              focusNode: form.submitFocusNode,
              onPressed: () {
                form.submit();
              },
              child: Text('Submit'),
            ),
          ),
        ],
      ),
    );
  }
}

class ClipperWidget extends StatelessWidget {
  const ClipperWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return ClipPath(
      clipper: MyClipper(),
      child: Container(
        width: size.width,
        height: size.height / 2,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5d5778), Color(0xFF2d2942)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 80, left: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Welcome",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 40,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text(
                "Back",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 40,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();

    path.lineTo(0, size.height * 0.7);
    path.quadraticBezierTo(
        size.width * 0.5, size.height, size.width * 0.58, size.height * 0.60);
    path.quadraticBezierTo(size.width * 0.60, size.height * 0.5,
        size.width * 0.75, size.height * 0.4);
    path.quadraticBezierTo(
        size.width * 0.95, size.height * 0.3, size.width, size.height * 0.2);
    path.lineTo(size.width, 0);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}
