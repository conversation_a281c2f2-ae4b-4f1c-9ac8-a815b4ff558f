import 'package:campus_find/constants/colors.dart';
import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/data/exceptions/sign_in_exception.dart';
import 'package:campus_find/data/models/auth.dart';
import 'package:campus_find/ui/auth/login_page.dart';
import 'package:flutter/material.dart';
import 'package:form_field_validator/form_field_validator.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

final _email = RM.injectTextEditing(
  validators: [
    requiredValidator,
    EmailValidator(errorText: 'enter a valid email address'),
  ],
);
final _password = RM.injectTextEditing(
  validators: [
    requiredValidator,
    MinLengthValidator(5, errorText: "Enter a password greater than 5")
  ],
);

final _registerForm = RM.injectForm(
    autovalidateMode: AutovalidateMode.onUserInteraction,
    autoFocusOnFirstError: true,
    submit: () async {
      print("Email: ${_email.state}, Pasword: ${_password.state}");
      await user.auth.signUp(
        (_) => AuthParam(
          email: _email.state,
          password: _password.state,
          signUp: SignUp.withEmailAndPassword,
        ),
      );
      if (user.error is EmailException) {
        print(user.error.message);
        _email.error = user.error.message;
      }
      if (user.error is PasswordException) {
        _password.error = user.error.message;
      }
    },
    onSubmitting: () {
      // called while waiting for form submission,
    },
    onSubmitted: () {
      // called after form is successfully submitted
      // For example navigation to user page
    });

class RegisterPage extends StatelessWidget {
  const RegisterPage({Key? key});
  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          ClipPath(
            clipper: MyClipper(),
            child: Container(
              width: size.width,
              height: size.height / 2,
              decoration: BoxDecoration(
                  // gradient: LinearGradient(
                  //   colors: [Color(0xFF5d5778), Color(0xFF2d2942)],
                  //   begin: Alignment.topLeft,
                  //   end: Alignment.bottomRight,
                  // ),
                  color: AppColors.primaryColor),
              child: Padding(
                padding: const EdgeInsets.only(top: 70, left: 30),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Welcome to",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 30,
                          fontWeight: FontWeight.bold,
                          fontStyle: FontStyle.italic),
                    ),
                    SizedBox(height: 10),
                    Text(
                      "Campus find",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 45,
                          fontWeight: FontWeight.bold,
                          fontFamily: "Montserrat"),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Center(
            child: Container(
              padding: EdgeInsets.only(left: 30, right: 30, top: 300),
              decoration: BoxDecoration(
                  // gradient: LinearGradient(
                  //   colors: [Color(0xFF5d5778), Colors.white],
                  //   begin: Alignment.topLeft,
                  //   end: Alignment.bottomRight,
                  // ),
                  ),
              child: SingleChildScrollView(
                child: OnFormBuilder(
                  listenTo: _registerForm,
                  builder: () => Column(
                    children: [
                      TextFormField(
                          autofocus: true,
                          controller: _email.controller,
                          focusNode: _email.focusNode,
                          onEditingComplete: () =>
                              _password.focusNode.requestFocus(),
                          decoration: getInputDecor("Email", _email.error)),
                      SizedBox(height: 40),
                      TextFormField(
                        controller: _password.controller,
                        focusNode: _password.focusNode,
                        // onEditingComplete: () => passwordController.focusNode.requestFocus(),
                        decoration: getInputDecor("Password", _password.error),
                        obscureText: true,
                      ),
                      SizedBox(
                        height: 70,
                      ),

                      registerButton(),
                      SizedBox(height: 20),
                      loginButton(context),
                      SizedBox(height: 20),
                      // googleButton(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget loginButton(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: () {
            _email.focusNode.unfocus();
            _password.focusNode.unfocus();
            RM.navigate.back();
          },
          child: Text(
            "Sign up",
            style: TextStyle(fontSize: 20),
          ),
        )
      ],
    );
  }

  Widget registerButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          elevation: 10,
          shadowColor: AppColors.secondaryColor,
          shape: StadiumBorder(),
          padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
        ),
        onPressed: () {
          _email.focusNode.unfocus();
          _password.focusNode.unfocus();
          if (_registerForm.validate()) {
            _registerForm.submit();
          }
        },
        child: Text(
          "Register",
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Widget forgotPasswordButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: Text(
        "Forgot password?",
        style: TextStyle(fontSize: 18, color: Colors.redAccent),
      ),
    );
  }

  // Widget googleButton() {
  //   return GoogleAuthButton(
  //     onPressed: () async {
  //       _showMyDialog();
  //       String message = await auth.signInWithGoogle();
  //       if (message == "Signed In") {
  //         await Provider.of<UserController>(context, listen: false).initUser();
  //       }
  //     },
  //   );
  // }

  InputDecoration getInputDecor(String text, String? errorText) {
    InputDecoration _inputDecoration = InputDecoration(
        labelText: text,
        labelStyle: TextStyle(color: Color(0xFF2D2943)),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Color(0xFF2D2943)),
          borderRadius: BorderRadius.circular(10),
        ),
        errorText: errorText);
    return _inputDecoration;
  }
}

class LoginForm extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return OnFormBuilder(
      listenTo: form,
      builder: () => Column(
        children: <Widget>[
          TextField(
            focusNode: _email.focusNode,
            controller: _email.controller,
            decoration: InputDecoration(
              errorText: _email.error,
            ),
            onSubmitted: (_) {
              //request the password node
              _password.focusNode.requestFocus();
            },
          ),
          TextField(
            focusNode: _password.focusNode,
            controller: _password.controller,
            decoration: new InputDecoration(
              errorText: _password.error,
            ),
            onSubmitted: (_) {
              //request the submit button node
              form.submitFocusNode.requestFocus();
            },
          ),
          OnFormSubmissionBuilder(
            listenTo: form,
            onSubmitting: () => Text("Submitting"),
            child: ElevatedButton(
              focusNode: form.submitFocusNode,
              onPressed: () => form.submit(),
              child: Text('Submit'),
            ),
          ),
        ],
      ),
    );
  }
}

class ClipperWidget extends StatelessWidget {
  const ClipperWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return ClipPath(
      clipper: MyClipper(),
      child: Container(
        width: size.width,
        height: size.height / 2,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF5d5778), Color(0xFF2d2942)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 80, left: 30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Welcome",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 40,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text(
                "Back",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 40,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MyClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    var path = Path();

    path.lineTo(0, size.height * 0.7);
    path.quadraticBezierTo(
        size.width * 0.5, size.height, size.width * 0.58, size.height * 0.60);
    path.quadraticBezierTo(size.width * 0.60, size.height * 0.5,
        size.width * 0.75, size.height * 0.4);
    path.quadraticBezierTo(
        size.width * 0.95, size.height * 0.3, size.width, size.height * 0.2);
    path.lineTo(size.width, 0);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return true;
  }
}
