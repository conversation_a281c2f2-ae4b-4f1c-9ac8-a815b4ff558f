import 'package:campus_find/data/controllers/auth_controller.dart';
import 'package:campus_find/ui/app/accomodation/accomodation_home.dart';
import 'package:campus_find/ui/auth/login_page.dart';
import 'package:campus_find/ui/auth/upload_images.dart';
import 'package:flutter/material.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class AuthPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox.expand(
        child: OnAuthBuilder(
          listenTo: user,
          onInitialWaiting: () => Center(
            child: CircularProgressIndicator(),
          ),
          onUnsigned: () => LoginPage(),
          onSigned: () {
            return user.state?.photoUrl == null
                ? UploadImage()
                : AccommodationHome();
          },
        ),
      ),
    );
  }
}
