import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/models/user_model.dart';

/// Authentication state
class AuthState {
  final User? firebaseUser;
  final UserModel? userProfile;
  final bool isLoading;
  final String? error;

  const AuthState({
    this.firebaseUser,
    this.userProfile,
    this.isLoading = false,
    this.error,
  });

  AuthState copyWith({
    User? firebaseUser,
    UserModel? userProfile,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      firebaseUser: firebaseUser ?? this.firebaseUser,
      userProfile: userProfile ?? this.userProfile,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  bool get isAuthenticated => firebaseUser != null;
  bool get hasProfile => userProfile != null;
  bool get isEmailVerified => firebaseUser?.emailVerified ?? false;
}

/// Authentication state notifier
class AuthStateNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthStateNotifier(this._authService) : super(const AuthState()) {
    // Listen to auth state changes
    _authService.authStateChanges.listen(_onAuthStateChanged);
  }

  Future<void> _onAuthStateChanged(User? user) async {
    if (user != null) {
      state = state.copyWith(firebaseUser: user, isLoading: true);
      
      // Load user profile
      try {
        final userProfile = await _authService.getUserProfile(user.uid);
        state = state.copyWith(
          userProfile: userProfile,
          isLoading: false,
          error: null,
        );
      } catch (e) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to load user profile: $e',
        );
      }
    } else {
      state = const AuthState();
    }
  }

  Future<void> signIn(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      // Auth state change will be handled by the listener
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> signUp({
    required String email,
    required String password,
    required String fullName,
    String? phoneNumber,
    String? campus,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _authService.createUserWithEmailAndPassword(
        email: email,
        password: password,
        fullName: fullName,
        phoneNumber: phoneNumber,
        campus: campus,
      );
      // Auth state change will be handled by the listener
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> signOut() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _authService.signOut();
      // Auth state change will be handled by the listener
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _authService.sendPasswordResetEmail(email);
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> sendEmailVerification() async {
    try {
      await _authService.sendEmailVerification();
    } catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }

  Future<void> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _authService.signInWithGoogle();
      // Auth state change will be handled by the listener
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> signInWithFacebook() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _authService.signInWithFacebook();
      // Auth state change will be handled by the listener
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      rethrow;
    }
  }

  Future<void> updateProfile(Map<String, dynamic> data) async {
    if (state.firebaseUser == null) return;
    
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      await _authService.updateUserProfile(state.firebaseUser!.uid, data);
      
      // Reload user profile
      final updatedProfile = await _authService.getUserProfile(state.firebaseUser!.uid);
      state = state.copyWith(
        userProfile: updatedProfile,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update profile: $e',
      );
      rethrow;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Authentication state provider
final authStateProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthStateNotifier(authService);
});

/// Convenience providers
final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authStateProvider).firebaseUser;
});

final userProfileProvider = Provider<UserModel?>((ref) {
  return ref.watch(authStateProvider).userProfile;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authStateProvider).isAuthenticated;
});

final isEmailVerifiedProvider = Provider<bool>((ref) {
  return ref.watch(authStateProvider).isEmailVerified;
});