import 'package:campus_find/ui/widgets/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

class Utils {
  static void showErrorSnackbar(String title) => RM.scaffold.showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(10), bottom: Radius.circular(10))),
          content: Text(
            title,
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.red,
        ),
      );
  static void showSuccessSnackbar(String title, {Duration? duration}) =>
      RM.scaffold.showSnackBar(
        SnackBar(
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(10), bottom: Radius.circular(10))),
          content: Text(
            title,
            style: TextStyle(color: Colors.white),
          ),
          duration: duration ?? Duration(seconds: 4),
          backgroundColor: Colors.green,
        ),
      );

  static void showLoadingWidget({String? title}) => RM.navigate.toDialog(
        SimpleDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 30, vertical: 30),
          children: [LoadingWidget(text: title)],
        ),
        barrierDismissible: false,
      );
}
