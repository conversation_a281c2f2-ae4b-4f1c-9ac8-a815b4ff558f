import 'package:campus_find/shared/widgets/loading_widget.dart';
import 'package:flutter/material.dart';

class Utils {
  static void showErrorSnackbar(BuildContext context, String title) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
                top: Radius.circular(10), bottom: Radius.circular(10))),
        content: Text(
          title,
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.red,
      ),
    );
  }

  static void showSuccessSnackbar(BuildContext context, String title, {Duration? duration}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(
                top: Radius.circular(10), bottom: Radius.circular(10))),
        content: Text(
          title,
          style: TextStyle(color: Colors.white),
        ),
        duration: duration ?? Duration(seconds: 4),
        backgroundColor: Colors.green,
      ),
    );
  }

  static void showLoadingWidget(BuildContext context, {String? title}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => SimpleDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 30, vertical: 30),
        children: [LoadingWidget(text: title)],
      ),
    );
  }
}
