// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserModel _$UserModelFromJson(Map<String, dynamic> json) {
  return _UserModel.fromJson(json);
}

/// @nodoc
mixin _$UserModel {
  String get id => throw _privateConstructorUsedError;
  String get email => throw _privateConstructorUsedError;
  String get fullName => throw _privateConstructorUsedError;
  String? get phoneNumber => throw _privateConstructorUsedError;
  String? get campus => throw _privateConstructorUsedError;
  String? get profileImageUrl => throw _privateConstructorUsedError;
  UserPreferences get preferences => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime? get lastLoginAt => throw _privateConstructorUsedError;
  bool get isVerified => throw _privateConstructorUsedError;
  UserRole get role => throw _privateConstructorUsedError;

  /// Serializes this UserModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserModelCopyWith<UserModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserModelCopyWith<$Res> {
  factory $UserModelCopyWith(UserModel value, $Res Function(UserModel) then) =
      _$UserModelCopyWithImpl<$Res, UserModel>;
  @useResult
  $Res call(
      {String id,
      String email,
      String fullName,
      String? phoneNumber,
      String? campus,
      String? profileImageUrl,
      UserPreferences preferences,
      DateTime createdAt,
      DateTime? lastLoginAt,
      bool isVerified,
      UserRole role});

  $UserPreferencesCopyWith<$Res> get preferences;
}

/// @nodoc
class _$UserModelCopyWithImpl<$Res, $Val extends UserModel>
    implements $UserModelCopyWith<$Res> {
  _$UserModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? fullName = null,
    Object? phoneNumber = freezed,
    Object? campus = freezed,
    Object? profileImageUrl = freezed,
    Object? preferences = null,
    Object? createdAt = null,
    Object? lastLoginAt = freezed,
    Object? isVerified = null,
    Object? role = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      campus: freezed == campus
          ? _value.campus
          : campus // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as UserPreferences,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole,
    ) as $Val);
  }

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserPreferencesCopyWith<$Res> get preferences {
    return $UserPreferencesCopyWith<$Res>(_value.preferences, (value) {
      return _then(_value.copyWith(preferences: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UserModelImplCopyWith<$Res>
    implements $UserModelCopyWith<$Res> {
  factory _$$UserModelImplCopyWith(
          _$UserModelImpl value, $Res Function(_$UserModelImpl) then) =
      __$$UserModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String email,
      String fullName,
      String? phoneNumber,
      String? campus,
      String? profileImageUrl,
      UserPreferences preferences,
      DateTime createdAt,
      DateTime? lastLoginAt,
      bool isVerified,
      UserRole role});

  @override
  $UserPreferencesCopyWith<$Res> get preferences;
}

/// @nodoc
class __$$UserModelImplCopyWithImpl<$Res>
    extends _$UserModelCopyWithImpl<$Res, _$UserModelImpl>
    implements _$$UserModelImplCopyWith<$Res> {
  __$$UserModelImplCopyWithImpl(
      _$UserModelImpl _value, $Res Function(_$UserModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = null,
    Object? fullName = null,
    Object? phoneNumber = freezed,
    Object? campus = freezed,
    Object? profileImageUrl = freezed,
    Object? preferences = null,
    Object? createdAt = null,
    Object? lastLoginAt = freezed,
    Object? isVerified = null,
    Object? role = null,
  }) {
    return _then(_$UserModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: null == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String,
      fullName: null == fullName
          ? _value.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String,
      phoneNumber: freezed == phoneNumber
          ? _value.phoneNumber
          : phoneNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      campus: freezed == campus
          ? _value.campus
          : campus // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImageUrl: freezed == profileImageUrl
          ? _value.profileImageUrl
          : profileImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      preferences: null == preferences
          ? _value.preferences
          : preferences // ignore: cast_nullable_to_non_nullable
              as UserPreferences,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      lastLoginAt: freezed == lastLoginAt
          ? _value.lastLoginAt
          : lastLoginAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isVerified: null == isVerified
          ? _value.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      role: null == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as UserRole,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserModelImpl extends _UserModel {
  const _$UserModelImpl(
      {required this.id,
      required this.email,
      required this.fullName,
      this.phoneNumber,
      this.campus,
      this.profileImageUrl,
      this.preferences = const UserPreferences(),
      required this.createdAt,
      this.lastLoginAt,
      this.isVerified = false,
      this.role = UserRole.student})
      : super._();

  factory _$UserModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserModelImplFromJson(json);

  @override
  final String id;
  @override
  final String email;
  @override
  final String fullName;
  @override
  final String? phoneNumber;
  @override
  final String? campus;
  @override
  final String? profileImageUrl;
  @override
  @JsonKey()
  final UserPreferences preferences;
  @override
  final DateTime createdAt;
  @override
  final DateTime? lastLoginAt;
  @override
  @JsonKey()
  final bool isVerified;
  @override
  @JsonKey()
  final UserRole role;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName, phoneNumber: $phoneNumber, campus: $campus, profileImageUrl: $profileImageUrl, preferences: $preferences, createdAt: $createdAt, lastLoginAt: $lastLoginAt, isVerified: $isVerified, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.phoneNumber, phoneNumber) ||
                other.phoneNumber == phoneNumber) &&
            (identical(other.campus, campus) || other.campus == campus) &&
            (identical(other.profileImageUrl, profileImageUrl) ||
                other.profileImageUrl == profileImageUrl) &&
            (identical(other.preferences, preferences) ||
                other.preferences == preferences) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.lastLoginAt, lastLoginAt) ||
                other.lastLoginAt == lastLoginAt) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.role, role) || other.role == role));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      email,
      fullName,
      phoneNumber,
      campus,
      profileImageUrl,
      preferences,
      createdAt,
      lastLoginAt,
      isVerified,
      role);

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      __$$UserModelImplCopyWithImpl<_$UserModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserModelImplToJson(
      this,
    );
  }
}

abstract class _UserModel extends UserModel {
  const factory _UserModel(
      {required final String id,
      required final String email,
      required final String fullName,
      final String? phoneNumber,
      final String? campus,
      final String? profileImageUrl,
      final UserPreferences preferences,
      required final DateTime createdAt,
      final DateTime? lastLoginAt,
      final bool isVerified,
      final UserRole role}) = _$UserModelImpl;
  const _UserModel._() : super._();

  factory _UserModel.fromJson(Map<String, dynamic> json) =
      _$UserModelImpl.fromJson;

  @override
  String get id;
  @override
  String get email;
  @override
  String get fullName;
  @override
  String? get phoneNumber;
  @override
  String? get campus;
  @override
  String? get profileImageUrl;
  @override
  UserPreferences get preferences;
  @override
  DateTime get createdAt;
  @override
  DateTime? get lastLoginAt;
  @override
  bool get isVerified;
  @override
  UserRole get role;

  /// Create a copy of UserModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserModelImplCopyWith<_$UserModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) {
  return _UserPreferences.fromJson(json);
}

/// @nodoc
mixin _$UserPreferences {
  double get maxBudget => throw _privateConstructorUsedError;
  List<String> get preferredAmenities => throw _privateConstructorUsedError;
  AccommodationType get preferredType => throw _privateConstructorUsedError;
  double get maxDistanceFromCampus => throw _privateConstructorUsedError;
  bool get receiveNotifications => throw _privateConstructorUsedError;
  String get preferredCurrency => throw _privateConstructorUsedError;

  /// Serializes this UserPreferences to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserPreferencesCopyWith<UserPreferences> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserPreferencesCopyWith<$Res> {
  factory $UserPreferencesCopyWith(
          UserPreferences value, $Res Function(UserPreferences) then) =
      _$UserPreferencesCopyWithImpl<$Res, UserPreferences>;
  @useResult
  $Res call(
      {double maxBudget,
      List<String> preferredAmenities,
      AccommodationType preferredType,
      double maxDistanceFromCampus,
      bool receiveNotifications,
      String preferredCurrency});
}

/// @nodoc
class _$UserPreferencesCopyWithImpl<$Res, $Val extends UserPreferences>
    implements $UserPreferencesCopyWith<$Res> {
  _$UserPreferencesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxBudget = null,
    Object? preferredAmenities = null,
    Object? preferredType = null,
    Object? maxDistanceFromCampus = null,
    Object? receiveNotifications = null,
    Object? preferredCurrency = null,
  }) {
    return _then(_value.copyWith(
      maxBudget: null == maxBudget
          ? _value.maxBudget
          : maxBudget // ignore: cast_nullable_to_non_nullable
              as double,
      preferredAmenities: null == preferredAmenities
          ? _value.preferredAmenities
          : preferredAmenities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      preferredType: null == preferredType
          ? _value.preferredType
          : preferredType // ignore: cast_nullable_to_non_nullable
              as AccommodationType,
      maxDistanceFromCampus: null == maxDistanceFromCampus
          ? _value.maxDistanceFromCampus
          : maxDistanceFromCampus // ignore: cast_nullable_to_non_nullable
              as double,
      receiveNotifications: null == receiveNotifications
          ? _value.receiveNotifications
          : receiveNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredCurrency: null == preferredCurrency
          ? _value.preferredCurrency
          : preferredCurrency // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserPreferencesImplCopyWith<$Res>
    implements $UserPreferencesCopyWith<$Res> {
  factory _$$UserPreferencesImplCopyWith(_$UserPreferencesImpl value,
          $Res Function(_$UserPreferencesImpl) then) =
      __$$UserPreferencesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double maxBudget,
      List<String> preferredAmenities,
      AccommodationType preferredType,
      double maxDistanceFromCampus,
      bool receiveNotifications,
      String preferredCurrency});
}

/// @nodoc
class __$$UserPreferencesImplCopyWithImpl<$Res>
    extends _$UserPreferencesCopyWithImpl<$Res, _$UserPreferencesImpl>
    implements _$$UserPreferencesImplCopyWith<$Res> {
  __$$UserPreferencesImplCopyWithImpl(
      _$UserPreferencesImpl _value, $Res Function(_$UserPreferencesImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxBudget = null,
    Object? preferredAmenities = null,
    Object? preferredType = null,
    Object? maxDistanceFromCampus = null,
    Object? receiveNotifications = null,
    Object? preferredCurrency = null,
  }) {
    return _then(_$UserPreferencesImpl(
      maxBudget: null == maxBudget
          ? _value.maxBudget
          : maxBudget // ignore: cast_nullable_to_non_nullable
              as double,
      preferredAmenities: null == preferredAmenities
          ? _value._preferredAmenities
          : preferredAmenities // ignore: cast_nullable_to_non_nullable
              as List<String>,
      preferredType: null == preferredType
          ? _value.preferredType
          : preferredType // ignore: cast_nullable_to_non_nullable
              as AccommodationType,
      maxDistanceFromCampus: null == maxDistanceFromCampus
          ? _value.maxDistanceFromCampus
          : maxDistanceFromCampus // ignore: cast_nullable_to_non_nullable
              as double,
      receiveNotifications: null == receiveNotifications
          ? _value.receiveNotifications
          : receiveNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredCurrency: null == preferredCurrency
          ? _value.preferredCurrency
          : preferredCurrency // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserPreferencesImpl implements _UserPreferences {
  const _$UserPreferencesImpl(
      {this.maxBudget = 50000.0,
      final List<String> preferredAmenities = const [],
      this.preferredType = AccommodationType.any,
      this.maxDistanceFromCampus = 5.0,
      this.receiveNotifications = true,
      this.preferredCurrency = 'NGN'})
      : _preferredAmenities = preferredAmenities;

  factory _$UserPreferencesImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserPreferencesImplFromJson(json);

  @override
  @JsonKey()
  final double maxBudget;
  final List<String> _preferredAmenities;
  @override
  @JsonKey()
  List<String> get preferredAmenities {
    if (_preferredAmenities is EqualUnmodifiableListView)
      return _preferredAmenities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_preferredAmenities);
  }

  @override
  @JsonKey()
  final AccommodationType preferredType;
  @override
  @JsonKey()
  final double maxDistanceFromCampus;
  @override
  @JsonKey()
  final bool receiveNotifications;
  @override
  @JsonKey()
  final String preferredCurrency;

  @override
  String toString() {
    return 'UserPreferences(maxBudget: $maxBudget, preferredAmenities: $preferredAmenities, preferredType: $preferredType, maxDistanceFromCampus: $maxDistanceFromCampus, receiveNotifications: $receiveNotifications, preferredCurrency: $preferredCurrency)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserPreferencesImpl &&
            (identical(other.maxBudget, maxBudget) ||
                other.maxBudget == maxBudget) &&
            const DeepCollectionEquality()
                .equals(other._preferredAmenities, _preferredAmenities) &&
            (identical(other.preferredType, preferredType) ||
                other.preferredType == preferredType) &&
            (identical(other.maxDistanceFromCampus, maxDistanceFromCampus) ||
                other.maxDistanceFromCampus == maxDistanceFromCampus) &&
            (identical(other.receiveNotifications, receiveNotifications) ||
                other.receiveNotifications == receiveNotifications) &&
            (identical(other.preferredCurrency, preferredCurrency) ||
                other.preferredCurrency == preferredCurrency));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      maxBudget,
      const DeepCollectionEquality().hash(_preferredAmenities),
      preferredType,
      maxDistanceFromCampus,
      receiveNotifications,
      preferredCurrency);

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserPreferencesImplCopyWith<_$UserPreferencesImpl> get copyWith =>
      __$$UserPreferencesImplCopyWithImpl<_$UserPreferencesImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserPreferencesImplToJson(
      this,
    );
  }
}

abstract class _UserPreferences implements UserPreferences {
  const factory _UserPreferences(
      {final double maxBudget,
      final List<String> preferredAmenities,
      final AccommodationType preferredType,
      final double maxDistanceFromCampus,
      final bool receiveNotifications,
      final String preferredCurrency}) = _$UserPreferencesImpl;

  factory _UserPreferences.fromJson(Map<String, dynamic> json) =
      _$UserPreferencesImpl.fromJson;

  @override
  double get maxBudget;
  @override
  List<String> get preferredAmenities;
  @override
  AccommodationType get preferredType;
  @override
  double get maxDistanceFromCampus;
  @override
  bool get receiveNotifications;
  @override
  String get preferredCurrency;

  /// Create a copy of UserPreferences
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserPreferencesImplCopyWith<_$UserPreferencesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
