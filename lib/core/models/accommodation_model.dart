import 'package:google_maps_flutter/google_maps_flutter.dart';

class AccommodationModel {
  final String id;
  final String title;
  final String description;
  final AccommodationType type;
  final LocationInfo location;
  final PricingInfo pricing;
  final List<String> amenities;
  final List<String> imageUrls;
  final ContactInfo contact;
  final double rating;
  final int reviewCount;
  final bool isVerified;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? ownerId;
  final Map<String, dynamic> metadata;

  const AccommodationModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.location,
    required this.pricing,
    this.amenities = const [],
    this.imageUrls = const [],
    required this.contact,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isVerified = false,
    this.isAvailable = true,
    required this.createdAt,
    this.updatedAt,
    this.ownerId,
    this.metadata = const {},
  });

  factory AccommodationModel.fromJson(Map<String, dynamic> json) {
    return AccommodationModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: AccommodationType.values.firstWhere(
        (e) => e.toString().split('.').last == json['type'],
        orElse: () => AccommodationType.any,
      ),
      location: LocationInfo.fromJson(json['location'] as Map<String, dynamic>),
      pricing: PricingInfo.fromJson(json['pricing'] as Map<String, dynamic>),
      amenities: (json['amenities'] as List<dynamic>?)?.cast<String>() ?? [],
      imageUrls: (json['imageUrls'] as List<dynamic>?)?.cast<String>() ?? [],
      contact: ContactInfo.fromJson(json['contact'] as Map<String, dynamic>),
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      reviewCount: json['reviewCount'] as int? ?? 0,
      isVerified: json['isVerified'] as bool? ?? false,
      isAvailable: json['isAvailable'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      ownerId: json['ownerId'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.toString().split('.').last,
      'location': location.toJson(),
      'pricing': pricing.toJson(),
      'amenities': amenities,
      'imageUrls': imageUrls,
      'contact': contact.toJson(),
      'rating': rating,
      'reviewCount': reviewCount,
      'isVerified': isVerified,
      'isAvailable': isAvailable,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'ownerId': ownerId,
      'metadata': metadata,
    };
  }
}

class LocationInfo {
  final double latitude;
  final double longitude;
  final String address;
  final String? city;
  final String? state;
  final String? country;
  final String? nearestCampus;
  final double distanceFromCampus;

  const LocationInfo({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.city,
    this.state,
    this.country,
    this.nearestCampus,
    this.distanceFromCampus = 0.0,
  });

  // Helper method to get LatLng
  LatLng get latLng => LatLng(latitude, longitude);

  factory LocationInfo.fromJson(Map<String, dynamic> json) {
    return LocationInfo(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      nearestCampus: json['nearestCampus'] as String?,
      distanceFromCampus:
          (json['distanceFromCampus'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'nearestCampus': nearestCampus,
      'distanceFromCampus': distanceFromCampus,
    };
  }
}

class PricingInfo {
  final double monthly;
  final double? yearly;
  final double? securityDeposit;
  final double? agentFee;
  final String currency;
  final bool negotiable;
  final String? paymentTerms;

  const PricingInfo({
    required this.monthly,
    this.yearly,
    this.securityDeposit,
    this.agentFee,
    this.currency = 'NGN',
    this.negotiable = false,
    this.paymentTerms,
  });

  factory PricingInfo.fromJson(Map<String, dynamic> json) {
    return PricingInfo(
      monthly: (json['monthly'] as num).toDouble(),
      yearly: (json['yearly'] as num?)?.toDouble(),
      securityDeposit: (json['securityDeposit'] as num?)?.toDouble(),
      agentFee: (json['agentFee'] as num?)?.toDouble(),
      currency: json['currency'] as String? ?? 'NGN',
      negotiable: json['negotiable'] as bool? ?? false,
      paymentTerms: json['paymentTerms'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'monthly': monthly,
      'yearly': yearly,
      'securityDeposit': securityDeposit,
      'agentFee': agentFee,
      'currency': currency,
      'negotiable': negotiable,
      'paymentTerms': paymentTerms,
    };
  }
}

class ContactInfo {
  final String name;
  final String phone;
  final String? email;
  final String? whatsapp;
  final List<String> availableHours;

  const ContactInfo({
    required this.name,
    required this.phone,
    this.email,
    this.whatsapp,
    this.availableHours = const [],
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      name: json['name'] as String,
      phone: json['phone'] as String,
      email: json['email'] as String?,
      whatsapp: json['whatsapp'] as String?,
      availableHours:
          (json['availableHours'] as List<dynamic>?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'email': email,
      'whatsapp': whatsapp,
      'availableHours': availableHours,
    };
  }
}

enum AccommodationType {
  hostel,
  apartment,
  sharedRoom,
  singleRoom,
  studio,
  any,
}
