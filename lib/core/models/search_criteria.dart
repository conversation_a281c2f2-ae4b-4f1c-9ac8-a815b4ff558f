import 'accommodation_model.dart';

class SearchCriteria {
  final String? query;
  final String? location;
  final double? minPrice;
  final double? maxPrice;
  final List<String> amenities;
  final AccommodationType? type;
  final double? maxDistanceFromCampus;
  final bool? isVerified;
  final bool? isAvailable;
  final SortOption sortBy;
  final SortOrder sortOrder;
  final int limit;
  final int offset;

  const SearchCriteria({
    this.query,
    this.location,
    this.minPrice,
    this.maxPrice,
    this.amenities = const [],
    this.type,
    this.maxDistanceFromCampus,
    this.isVerified,
    this.isAvailable,
    this.sortBy = SortOption.relevance,
    this.sortOrder = SortOrder.descending,
    this.limit = 20,
    this.offset = 0,
  });

  SearchCriteria copyWith({
    String? query,
    String? location,
    double? minPrice,
    double? maxPrice,
    List<String>? amenities,
    AccommodationType? type,
    double? maxDistanceFromCampus,
    bool? isVerified,
    bool? isAvailable,
    SortOption? sortBy,
    SortOrder? sortOrder,
    int? limit,
    int? offset,
  }) {
    return SearchCriteria(
      query: query ?? this.query,
      location: location ?? this.location,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      amenities: amenities ?? this.amenities,
      type: type ?? this.type,
      maxDistanceFromCampus: maxDistanceFromCampus ?? this.maxDistanceFromCampus,
      isVerified: isVerified ?? this.isVerified,
      isAvailable: isAvailable ?? this.isAvailable,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'query': query,
      'location': location,
      'minPrice': minPrice,
      'maxPrice': maxPrice,
      'amenities': amenities,
      'type': type?.toString().split('.').last,
      'maxDistanceFromCampus': maxDistanceFromCampus,
      'isVerified': isVerified,
      'isAvailable': isAvailable,
      'sortBy': sortBy.toString().split('.').last,
      'sortOrder': sortOrder.toString().split('.').last,
      'limit': limit,
      'offset': offset,
    };
  }

  factory SearchCriteria.fromJson(Map<String, dynamic> json) {
    return SearchCriteria(
      query: json['query'] as String?,
      location: json['location'] as String?,
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      amenities: (json['amenities'] as List<dynamic>?)?.cast<String>() ?? [],
      type: json['type'] != null
          ? AccommodationType.values.firstWhere(
              (e) => e.toString().split('.').last == json['type'],
              orElse: () => AccommodationType.any,
            )
          : null,
      maxDistanceFromCampus: (json['maxDistanceFromCampus'] as num?)?.toDouble(),
      isVerified: json['isVerified'] as bool?,
      isAvailable: json['isAvailable'] as bool?,
      sortBy: SortOption.values.firstWhere(
        (e) => e.toString().split('.').last == json['sortBy'],
        orElse: () => SortOption.relevance,
      ),
      sortOrder: SortOrder.values.firstWhere(
        (e) => e.toString().split('.').last == json['sortOrder'],
        orElse: () => SortOrder.descending,
      ),
      limit: json['limit'] as int? ?? 20,
      offset: json['offset'] as int? ?? 0,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SearchCriteria &&
        other.query == query &&
        other.location == location &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        _listEquals(other.amenities, amenities) &&
        other.type == type &&
        other.maxDistanceFromCampus == maxDistanceFromCampus &&
        other.isVerified == isVerified &&
        other.isAvailable == isAvailable &&
        other.sortBy == sortBy &&
        other.sortOrder == sortOrder &&
        other.limit == limit &&
        other.offset == offset;
  }

  @override
  int get hashCode {
    return Object.hash(
      query,
      location,
      minPrice,
      maxPrice,
      Object.hashAll(amenities),
      type,
      maxDistanceFromCampus,
      isVerified,
      isAvailable,
      sortBy,
      sortOrder,
      limit,
      offset,
    );
  }

  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}

enum SortOption {
  relevance,
  price,
  rating,
  distance,
  newest,
  oldest,
}

enum SortOrder {
  ascending,
  descending,
}

class SearchResult {
  final List<AccommodationModel> accommodations;
  final int totalCount;
  final bool hasMore;
  final SearchCriteria criteria;

  const SearchResult({
    required this.accommodations,
    required this.totalCount,
    required this.hasMore,
    required this.criteria,
  });

  SearchResult copyWith({
    List<AccommodationModel>? accommodations,
    int? totalCount,
    bool? hasMore,
    SearchCriteria? criteria,
  }) {
    return SearchResult(
      accommodations: accommodations ?? this.accommodations,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
      criteria: criteria ?? this.criteria,
    );
  }
}

class FilterOptions {
  final List<String> availableAmenities;
  final List<AccommodationType> availableTypes;
  final PriceRange priceRange;
  final List<String> availableLocations;

  const FilterOptions({
    required this.availableAmenities,
    required this.availableTypes,
    required this.priceRange,
    required this.availableLocations,
  });

  factory FilterOptions.fromJson(Map<String, dynamic> json) {
    return FilterOptions(
      availableAmenities: (json['availableAmenities'] as List<dynamic>).cast<String>(),
      availableTypes: (json['availableTypes'] as List<dynamic>)
          .map((type) => AccommodationType.values.firstWhere(
                (e) => e.toString().split('.').last == type,
                orElse: () => AccommodationType.any,
              ))
          .toList(),
      priceRange: PriceRange.fromJson(json['priceRange'] as Map<String, dynamic>),
      availableLocations: (json['availableLocations'] as List<dynamic>).cast<String>(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'availableAmenities': availableAmenities,
      'availableTypes': availableTypes.map((type) => type.toString().split('.').last).toList(),
      'priceRange': priceRange.toJson(),
      'availableLocations': availableLocations,
    };
  }
}

class PriceRange {
  final double min;
  final double max;

  const PriceRange({
    required this.min,
    required this.max,
  });

  factory PriceRange.fromJson(Map<String, dynamic> json) {
    return PriceRange(
      min: (json['min'] as num).toDouble(),
      max: (json['max'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'min': min,
      'max': max,
    };
  }
}