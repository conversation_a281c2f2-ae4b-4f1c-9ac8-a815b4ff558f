import 'package:freezed_annotation/freezed_annotation.dart';
import 'accommodation_model.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String id,
    required String email,
    required String fullName,
    String? phoneNumber,
    String? campus,
    String? profileImageUrl,
    @Default(UserPreferences()) UserPreferences preferences,
    required DateTime createdAt,
    DateTime? lastLoginAt,
    @Default(false) bool isVerified,
    @Default(UserRole.student) UserRole role,
  }) = _UserModel;

  const UserModel._();

  factory UserModel.fromJson(Map<String, dynamic> json) => _$UserModelFromJson(json);

  /// Validates the user model
  List<String> validate() {
    final errors = <String>[];

    if (id.trim().isEmpty) {
      errors.add('User ID cannot be empty');
    }

    if (email.trim().isEmpty) {
      errors.add('Email cannot be empty');
    } else if (!_isValidEmail(email)) {
      errors.add('Invalid email format');
    }

    if (fullName.trim().isEmpty) {
      errors.add('Full name cannot be empty');
    } else if (fullName.trim().length < 2) {
      errors.add('Full name must be at least 2 characters long');
    }

    if (phoneNumber != null && phoneNumber!.isNotEmpty) {
      if (!_isValidPhoneNumber(phoneNumber!)) {
        errors.add('Invalid phone number format');
      }
    }

    return errors;
  }

  /// Checks if the user model is valid
  bool get isValid => validate().isEmpty;

  /// Validates email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Validates phone number format (basic validation)
  bool _isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    // Check if it has 10-15 digits (international format)
    return digitsOnly.length >= 10 && digitsOnly.length <= 15;
  }

  /// Creates a display name for the user
  String get displayName {
    return fullName.isNotEmpty ? fullName : email.split('@').first;
  }

  /// Checks if user has completed their profile
  bool get hasCompleteProfile {
    return fullName.isNotEmpty && 
           campus != null && 
           campus!.isNotEmpty &&
           phoneNumber != null && 
           phoneNumber!.isNotEmpty;
  }
}

@freezed
class UserPreferences with _$UserPreferences {
  const factory UserPreferences({
    @Default(50000.0) double maxBudget,
    @Default([]) List<String> preferredAmenities,
    @Default(AccommodationType.any) AccommodationType preferredType,
    @Default(5.0) double maxDistanceFromCampus,
    @Default(true) bool receiveNotifications,
    @Default('NGN') String preferredCurrency,
  }) = _UserPreferences;

  factory UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);
}

enum UserRole {
  student,
  campusRep,
  admin,
}