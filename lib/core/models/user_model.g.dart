// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserModelImpl _$$UserModelImplFromJson(Map<String, dynamic> json) =>
    _$UserModelImpl(
      id: json['id'] as String,
      email: json['email'] as String,
      fullName: json['fullName'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      campus: json['campus'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      preferences: json['preferences'] == null
          ? const UserPreferences()
          : UserPreferences.fromJson(
              json['preferences'] as Map<String, dynamic>),
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
      isVerified: json['isVerified'] as bool? ?? false,
      role: $enumDecodeNullable(_$UserRoleEnumMap, json['role']) ??
          UserRole.student,
    );

Map<String, dynamic> _$$UserModelImplToJson(_$UserModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'fullName': instance.fullName,
      'phoneNumber': instance.phoneNumber,
      'campus': instance.campus,
      'profileImageUrl': instance.profileImageUrl,
      'preferences': instance.preferences,
      'createdAt': instance.createdAt.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
      'isVerified': instance.isVerified,
      'role': _$UserRoleEnumMap[instance.role]!,
    };

const _$UserRoleEnumMap = {
  UserRole.student: 'student',
  UserRole.campusRep: 'campusRep',
  UserRole.admin: 'admin',
};

_$UserPreferencesImpl _$$UserPreferencesImplFromJson(
        Map<String, dynamic> json) =>
    _$UserPreferencesImpl(
      maxBudget: (json['maxBudget'] as num?)?.toDouble() ?? 50000.0,
      preferredAmenities: (json['preferredAmenities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      preferredType: $enumDecodeNullable(
              _$AccommodationTypeEnumMap, json['preferredType']) ??
          AccommodationType.any,
      maxDistanceFromCampus:
          (json['maxDistanceFromCampus'] as num?)?.toDouble() ?? 5.0,
      receiveNotifications: json['receiveNotifications'] as bool? ?? true,
      preferredCurrency: json['preferredCurrency'] as String? ?? 'NGN',
    );

Map<String, dynamic> _$$UserPreferencesImplToJson(
        _$UserPreferencesImpl instance) =>
    <String, dynamic>{
      'maxBudget': instance.maxBudget,
      'preferredAmenities': instance.preferredAmenities,
      'preferredType': _$AccommodationTypeEnumMap[instance.preferredType]!,
      'maxDistanceFromCampus': instance.maxDistanceFromCampus,
      'receiveNotifications': instance.receiveNotifications,
      'preferredCurrency': instance.preferredCurrency,
    };

const _$AccommodationTypeEnumMap = {
  AccommodationType.hostel: 'hostel',
  AccommodationType.apartment: 'apartment',
  AccommodationType.sharedRoom: 'sharedRoom',
  AccommodationType.singleRoom: 'singleRoom',
  AccommodationType.studio: 'studio',
  AccommodationType.any: 'any',
};
