import 'package:flutter/material.dart';
import '../config/environment.dart';

/// Google Maps configuration and utilities (disabled for now)
class MapsConfig {
  // Google Maps API Key
  static String get apiKey => Environment.googleMapsApiKey;
  
  // Default location coordinates (Lagos, Nigeria)
  static const double defaultLatitude = 6.5244;
  static const double defaultLongitude = 3.3792;

  /// Check and request location permissions (disabled for now)
  static Future<bool> checkLocationPermission() async {
    print('Location permission check skipped');
    return false;
  }

  /// Get current user location (disabled for now)
  static Future<Map<String, double>?> getCurrentLocation() async {
    print('Get current location skipped');
    return null;
  }

  /// Get address from coordinates (disabled for now)
  static Future<String?> getAddressFromCoordinates(double lat, double lng) async {
    print('Get address from coordinates skipped');
    return null;
  }

  /// Calculate distance between two points (basic implementation)
  static double calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    // Simple distance calculation (not accurate, just for demo)
    double deltaLat = lat2 - lat1;
    double deltaLng = lng2 - lng1;
    return (deltaLat * deltaLat + deltaLng * deltaLng) * 111; // Rough km conversion
  }
}