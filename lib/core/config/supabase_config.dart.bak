import 'package:supabase_flutter/supabase_flutter.dart';
import 'environment.dart';

/// Supabase configuration and initialization
class SupabaseConfig {
  static SupabaseClient? _client;
  
  static SupabaseClient get client {
    if (_client == null) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return _client!;
  }
  
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: Environment.supabaseUrl,
      anonKey: Environment.supabaseAnonKey,
      debug: Environment.isDevelopment,
    );
    _client = Supabase.instance.client;
  }
  
  static bool get isInitialized => _client != null;
}