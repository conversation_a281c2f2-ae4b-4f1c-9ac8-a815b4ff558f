import 'package:flutter/material.dart';
import '../../features/hostels/presentation/pages/accomodation_home.dart';
import '../../features/hostels/presentation/pages/create_request_add_images.dart';
import '../../features/hostels/presentation/pages/create_request_roommate_prefs.dart';
import '../../features/hostels/presentation/pages/create_request_success_page.dart';
import '../../features/hostels/presentation/pages/create_request_room_details.dart';
import '../../features/hostels/presentation/pages/lodge/create_lodge_more.dart';
import '../../features/hostels/presentation/pages/lodge/create_lodge_room_details.dart';
import '../../features/hostels/presentation/pages/lodge_interest.dart';
import '../../features/hostels/presentation/pages/request_interest.dart';
import '../../features/hostels/presentation/pages/roommate_details.dart';
import '../../shared/widgets/drawer_list.dart';
import '../../shared/widgets/notification_page.dart';
import '../../shared/widgets/user_page.dart';
import '../../features/hostels/presentation/pages/home_page.dart';
import '../../features/hostels/presentation/pages/test_page.dart';
import '../../features/authentication/presentation/pages/login_screen.dart';
import '../../features/authentication/presentation/pages/register_screen.dart';
import '../../features/authentication/presentation/pages/password_reset_screen.dart';
import '../../features/authentication/presentation/pages/email_verification_screen.dart';
import '../../features/authentication/presentation/pages/auth_page.dart';
import '../../features/profile/presentation/pages/profile_view_page.dart';
import '../../features/profile/presentation/pages/profile_edit_page.dart';
import 'routes.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;

    switch (settings.name) {
      // APP SPECIFIC
      case Routes.INITIAL:
        return MaterialPageRoute(builder: (_) => AuthPage());
      case Routes.LOGIN:
        return MaterialPageRoute(builder: (_) => LoginScreen());
      case Routes.REGISTER:
        return MaterialPageRoute(builder: (_) => RegisterScreen());
      case Routes.PASSWORD_RESET:
        return MaterialPageRoute(builder: (_) => PasswordResetScreen());
      case Routes.EMAIL_VERIFICATION:
        return MaterialPageRoute(builder: (_) => EmailVerificationScreen());
      case Routes.HOMEPAGE:
        return MaterialPageRoute(builder: (_) => HomePage());
      case Routes.TEST:
        return MaterialPageRoute(builder: (_) => TestPage());
      case Routes.NOTIFICATIONS:
        return MaterialPageRoute(builder: (_) => NotificationsPage());
      case Routes.USER_PAGE:
        return MaterialPageRoute(builder: (_) => UserPage());
      case Routes.DRAWER:
        return MaterialPageRoute(builder: (_) => AppDrawer());

      // ACCOMMODATION
      case Routes.ACCOMMODATION:
        return MaterialPageRoute(builder: (_) => AccommodationHome());
      case Routes.ROOMMATE_DETAILS:
        return MaterialPageRoute(builder: (_) => RoommateDetailsPage());

      // ROOMMATE
      case Routes.CREATE_REQUEST_ROOM_DETAILS:
        return MaterialPageRoute(builder: (_) => CreateRequestRoomDetails());
      case Routes.CREATE_REQUEST_ROOMMATE:
        return MaterialPageRoute(builder: (_) => CreateRequestRoommatePref());
      case Routes.CREATE_REQUEST_ADD_IMAGES:
        return MaterialPageRoute(builder: (_) => CreateRequestAddImages());
      case Routes.CREATE_REQUEST_SUCCESS:
        return MaterialPageRoute(builder: (_) => CreateRequestSuccessPage());
      case Routes.REQUEST_INTEREST_PAGE:
        return MaterialPageRoute(
          builder: (_) => RequestInterestPage(
            posterName: args as String? ?? '',
          ),
        );

      // LODGE
      case Routes.CREATE_LODGE_DETAILS:
        return MaterialPageRoute(builder: (_) => CreateLodgeRoomDetails());
      case Routes.CREATE_LODGE_MORE:
        return MaterialPageRoute(builder: (_) => CreateLodgeMore());
      case Routes.LODGE_INTEREST_PAGE:
        return MaterialPageRoute(
          builder: (_) => LodgeInterestPage(
            posterName: args as String? ?? '',
          ),
        );

      // PROFILE
      case Routes.PROFILE_VIEW:
        return MaterialPageRoute(builder: (_) => const ProfileViewPage());
      case Routes.PROFILE_EDIT:
        return MaterialPageRoute(builder: (_) => const ProfileEditPage());

      default:
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(
      builder: (_) {
        return Scaffold(
          appBar: AppBar(
            title: Text('Error'),
          ),
          body: Center(
            child: Text('ERROR: Route not found'),
          ),
        );
      },
    );
  }
}
