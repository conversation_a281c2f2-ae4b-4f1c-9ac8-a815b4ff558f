import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

/// Shared preferences provider
final sharedPreferencesProvider = Provider<SharedPreferences>((ref) {
  throw UnimplementedError('SharedPreferences must be initialized first');
});

/// Authentication state provider
final authStateProvider = StateProvider<AuthState>((ref) => AuthState.initial);

/// Authentication state enum
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Global error provider
final errorProvider = StateProvider<String?>((ref) => null);

/// Loading state provider
final loadingProvider = StateProvider<bool>((ref) => false);

/// Firebase Auth provider
final firebaseAuthProvider = Provider<FirebaseAuth>((ref) => FirebaseAuth.instance);

/// Firestore provider
final firestoreProvider = Provider<FirebaseFirestore>((ref) => FirebaseFirestore.instance);

/// Firebase Storage provider
final firebaseStorageProvider = Provider<FirebaseStorage>((ref) => FirebaseStorage.instance);