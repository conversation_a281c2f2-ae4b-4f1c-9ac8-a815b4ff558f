/// Payment gateway configuration (disabled for now)
class PaymentConfig {
  /// Initialize payment gateways (disabled for now)
  static Future<void> initializePaymentGateways() async {
    print('Payment gateway initialization skipped');
  }

  /// Supported payment methods
  static const List<PaymentMethod> supportedPaymentMethods = [
    PaymentMethod.card,
    PaymentMethod.bankTransfer,
    PaymentMethod.mobileMoney,
  ];

  /// Payment currencies
  static const List<String> supportedCurrencies = [
    'NGN', // Nigerian Naira
    'USD', // US Dollar
    'GHS', // Ghanaian Cedi
    'KES', // Kenyan Shilling
  ];

  /// Get currency symbol
  static String getCurrencySymbol(String currencyCode) {
    switch (currencyCode.toUpperCase()) {
      case 'NGN':
        return '₦';
      case 'USD':
        return '\$';
      case 'GHS':
        return '₵';
      case 'KES':
        return 'KSh';
      default:
        return currencyCode;
    }
  }

  /// Format amount with currency
  static String formatAmount(double amount, String currencyCode) {
    final symbol = getCurrencySymbol(currencyCode);
    return '$symbol${amount.toStringAsFixed(2)}';
  }
}

/// Payment method enum
enum PaymentMethod {
  card,
  bankTransfer,
  mobileMoney,
}

/// Payment status enum
enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded,
}