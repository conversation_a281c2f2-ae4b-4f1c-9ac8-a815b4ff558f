import '../../features/hostels/presentation/pages/accomodation_home.dart';
import '../../features/hostels/presentation/pages/create_request_add_images.dart';
import '../../features/hostels/presentation/pages/create_request_roommate_prefs.dart';
import '../../features/hostels/presentation/pages/create_request_success_page.dart';
import '../../features/hostels/presentation/pages/create_request_room_details.dart';
import '../../features/hostels/presentation/pages/lodge/create_lodge_more.dart';
import '../../features/hostels/presentation/pages/lodge/create_lodge_room_details.dart';
import '../../features/hostels/presentation/pages/lodge_interest.dart';
import '../../features/hostels/presentation/pages/request_interest.dart';
import '../../features/hostels/presentation/pages/roommate_details.dart';
import '../../shared/widgets/drawer_list.dart';
import '../../shared/widgets/notification_page.dart';
import '../../shared/widgets/user_page.dart';
import '../../features/hostels/presentation/pages/home_page.dart';
import '../../features/hostels/presentation/pages/test_page.dart';
import '../../features/authentication/presentation/pages/login_page.dart';
import '../../features/authentication/presentation/pages/register_screen.dart';
import '../../features/authentication/presentation/pages/auth_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:states_rebuilder/states_rebuilder.dart';

import 'routes.dart';

abstract class Pages {
  static final pages = <String, Widget Function(RouteData)>{
    // APP SPECIFIC
    Routes.INITIAL: (_) => AuthPage(),
    Routes.LOGIN: (_) => LoginPage(),
    Routes.REGISTER: (_) => RegisterPage(),
    Routes.HOMEPAGE: (_) => HomePage(),
    Routes.TEST: (_) => TestPage(),
    Routes.NOTIFICATIONS: (_) => NotificationsPage(),
    Routes.USER_PAGE: (_) => UserPage(),
    Routes.DRAWER: (_) => AppDrawer(),

    // ACCOMODATION
    Routes.ACCOMMODATION: (_) => AccommodationHome(),
    Routes.ROOMMATE_DETAILS: (_) => RoommateDetailsPage(),

    // ROOMMATE
    Routes.CREATE_REQUEST_ROOM_DETAILS: (_) => CreateRequestRoomDetails(),
    Routes.CREATE_REQUEST_ROOMMATE: (_) => CreateRequestRoommatePref(),
    Routes.CREATE_REQUEST_ADD_IMAGES: (_) => CreateRequestAddImages(),
    Routes.CREATE_REQUEST_SUCCESS: (_) => CreateRequestSuccessPage(),
    Routes.REQUEST_INTEREST_PAGE: (_) => RequestInterestPage(
          posterName: _.arguments as String,
        ),

    // lODGE
    Routes.CREATE_LODGE_DETAILS: (_) => CreateLodgeRoomDetails(),
    Routes.CREATE_LODGE_MORE: (_) => CreateLodgeMore(),
    Routes.LODGE_INTEREST_PAGE: (_) => LodgeInterestPage(
          posterName: _.arguments as String,
        ),

    // App Specific
  };
}
