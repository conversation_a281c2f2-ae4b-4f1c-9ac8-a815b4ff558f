import 'package:flutter/foundation.dart';
import 'firebase_config.dart';
import 'payment_config.dart';
import 'maps_config.dart';
import 'environment.dart';

/// Main application configuration
class AppConfig {
  /// Initialize all app services and configurations
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print('Initializing Campus Find app...');
      }

      // Initialize Firebase services
      await FirebaseConfig.initialize();
      if (kDebugMode) {
        print('✓ Firebase services initialized');
      }

      // Initialize payment gateways
      await PaymentConfig.initializePaymentGateways();
      if (kDebugMode) {
        print('✓ Payment gateways initialized');
      }

      // Validate configurations
      _validateConfigurations();
      if (kDebugMode) {
        print('✓ Configurations validated');
      }

      if (kDebugMode) {
        print('Campus Find app initialization completed successfully!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing app: $e');
      }
      rethrow;
    }
  }

  /// Validate all configurations
  static void _validateConfigurations() {
    final List<String> missingConfigs = [];

    // Check Google Maps API key
    if (MapsConfig.apiKey.isEmpty || MapsConfig.apiKey == 'your_google_maps_api_key_here') {
      missingConfigs.add('Google Maps API Key');
    }

    // Note: PayStack support can be added later when compatible version is available

    // Note: Stripe support can be added later when needed

    if (missingConfigs.isNotEmpty && kDebugMode) {
      print('⚠️  Warning: The following configurations are missing or using default values:');
      for (String config in missingConfigs) {
        print('   - $config');
      }
      print('   Please update your environment variables or configuration files.');
    }
  }

  /// Get app information
  static Map<String, dynamic> getAppInfo() {
    return {
      'name': 'Campus Find',
      'version': '1.0.0',
      'environment': Environment.isDevelopment ? 'development' : 'production',
      'debug': Environment.isDebug,
      'logging': Environment.enableLogging,
      'firebase_initialized': true,
      'payment_gateways': ['Stripe'],
      'supported_currencies': PaymentConfig.supportedCurrencies,
      'maps_provider': 'Google Maps',
    };
  }

  /// Check if app is properly configured
  static bool get isProperlyConfigured {
    return MapsConfig.apiKey.isNotEmpty;
  }
}