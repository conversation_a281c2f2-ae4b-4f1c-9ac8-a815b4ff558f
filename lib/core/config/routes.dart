abstract class Routes {
  static const INITIAL = '/';

  // Test
  static const TEST = '/test';

  // App Specific routes
  static const USER_PAGE = '/user';
  static const NOTIFICATIONS = '/notifications';

  // Auth
  static const LOGIN = '/login';
  static const REGISTER = '/register';
  static const PASSWORD_RESET = '/password-reset';
  static const EMAIL_VERIFICATION = '/email-verification';
  static const HOMEPAGE = '/homepage';
  static const DRAWER = '/drawer';

  // accommodation
  static const ACCOMMODATION = '/accommodation';
  static const SEARCH = '/search';

  // ROOMMATE
  static const ROOMMATE_DETAILS = '/roommate_details';
  static const REQUEST_INTEREST_PAGE = '/request_interest';

  static const CREATE_REQUEST_ROOM_DETAILS = '/create-new-rr';
  static const CREATE_REQUEST_ROOMMATE = '/create_request_roommate';
  static const CREATE_REQUEST_ADD_IMAGES = '/add_images';
  static const CREATE_REQUEST_SUCCESS = '/create_request_success';

  // LODGE
  static const LODGE_DETAILS = '/lodge_details';
  static const LODGE_INTEREST_PAGE = '/lodge_interest';
  static const CREATE_LODGE_DETAILS = '/create_lodge_1';
  static const CREATE_LODGE_MORE = 'create_lodge_2';

  // PROFILE
  static const PROFILE_VIEW = '/profile';
  static const PROFILE_EDIT = '/profile/edit';
}
