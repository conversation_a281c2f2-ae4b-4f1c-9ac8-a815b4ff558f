import 'package:flutter/material.dart';

abstract class AppColors {
  // Primary colors
  static const Color primary = Color(0xFF36337f);
  static const Color secondary = Color(0xFFecf1fd);
  
  // Background colors
  static const Color background = Color(0xFFFFFFFF);
  static const Color darkBackground = Color(0xFF121212);
  static const Color surface = Color(0xFFF5F5F5);
  
  // Text colors
  static const Color textPrimary = Color(0xFF1A1A1A);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textDisabled = Color(0xFF999999);
  
  // Status colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Border and divider colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFE0E0E0);
  
  // Legacy colors (for backward compatibility)
  static const Color primaryColor = primary;
  static const Color secondaryColor = secondary;
}
