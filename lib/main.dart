import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Core configurations
import 'core/config/riverpod_config.dart';
import 'core/config/route_generator.dart';
import 'core/config/routes.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize system UI
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarIconBrightness: Brightness.dark,
    statusBarColor: Colors.transparent,
  ));

  // Initialize core services
  await _initializeServices();

  runApp(
    ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(
          await SharedPreferences.getInstance(),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

/// Initialize all core services
Future<void> _initializeServices() async {
  try {
    // For now, we'll skip Firebase initialization to get the app running
    // await AppConfig.initialize();

    print('Basic services initialized successfully');
  } catch (e) {
    print('Error initializing services: $e');
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp(
      title: 'Campus Find',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        fontFamily: "SourceSansPro",
        primarySwatch: Colors.blue,
      ),
      initialRoute: Routes.LOGIN,
      onGenerateRoute: RouteGenerator.generateRoute,
    );
  }
}
